-- Script pour vérifier les rôles existants dans la base de données
-- Exécuter ce script pour voir tous les rôles actuellement présents

-- 1. Afficher tous les rôles
SELECT 
    "Id",
    "Name",
    "Description",
    "CreatedAt",
    "UpdatedAt"
FROM "Roles"
ORDER BY "Id";

-- 2. Compter le nombre de rôles
SELECT COUNT(*) as "NombreDeRoles" FROM "Roles";

-- 3. Afficher tous les utilisateurs avec leurs rôles
SELECT 
    u."Id" as "UserId",
    u."Username",
    u."Email",
    u."FirstName",
    u."LastName",
    r."Name" as "RoleName",
    ur."CreatedAt" as "RoleAssignedAt"
FROM "Users" u
LEFT JOIN "UserRoles" ur ON u."Id" = ur."UserId"
LEFT JOIN "Roles" r ON ur."RoleId" = r."Id"
ORDER BY u."Id", r."Name";

-- 4. Afficher les utilisateurs sans rôles
SELECT 
    u."Id",
    u."Username",
    u."Email",
    u."FirstName",
    u."LastName",
    u."CreatedAt"
FROM "Users" u
LEFT JOIN "UserRoles" ur ON u."Id" = ur."UserId"
WHERE ur."UserId" IS NULL
ORDER BY u."CreatedAt" DESC;

-- 5. Statistiques par rôle
SELECT 
    r."Name" as "RoleName",
    COUNT(ur."UserId") as "NombreUtilisateurs"
FROM "Roles" r
LEFT JOIN "UserRoles" ur ON r."Id" = ur."RoleId"
GROUP BY r."Id", r."Name"
ORDER BY r."Id";
