using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using NafaPlace.Catalog.Application.Common.Mappings;
using NafaPlace.Catalog.Application.DTOs.Category;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Application.Services;
using NafaPlace.Catalog.Domain.Models;
using NafaPlace.Catalog.Infrastructure.Persistence;
using Xunit;

namespace NafaPlace.Catalog.Tests.Categories
{
    public class CategoryServiceTests : IDisposable
    {
        private readonly CatalogDbContext _dbContext;
        private readonly IMapper _mapper;
        private readonly CategoryService _categoryService;

        public CategoryServiceTests()
        {
            var options = new DbContextOptionsBuilder<CatalogDbContext>()
                .UseInMemoryDatabase(databaseName: int.TryParse.ToString())
                .Options;

            _dbContext = new CatalogDbContext(options);
            
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<MappingProfile>();
            });
            _mapper = mapperConfig.CreateMapper();
            
            _categoryService = new CategoryService(_dbContext, _mapper);
        }

        public void Dispose()
        {
            _dbContext.Database.EnsureDeleted();
            _dbContext.Dispose();
        }

        [Fact]
        public async Task CreateCategory_ReturnsCategoryDto()
        {
            // Arrange
            var request = new CreateCategoryRequest
            {
                Name = "Test Category",
                Description = "Test Description",
                IconUrl = "https://example.com/icons/test.png",
                ImageUrl = "https://example.com/images/test.jpg"
            };

            // Act
            var result = await _categoryService.CreateCategoryAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(request.Name, result.Name);
            Assert.Equal(request.Description, result.Description);
        }

        [Fact]
        public async Task CreateSubcategory_WithValidParentId_ReturnsSubcategoryDto()
        {
            // Arrange
            var parentCategory = new Category
            {
                Name = "Parent Category",
                Description = "Parent Description",
                IconUrl = "https://example.com/icons/parent.png",
                ImageUrl = "https://example.com/images/parent.jpg"
            };
            _dbContext.Categories.Add(parentCategory);
            await _dbContext.SaveChangesAsync();

            var request = new CreateCategoryRequest
            {
                Name = "Test Subcategory",
                Description = "Test Description",
                ParentId = parentCategory.Id,
                IconUrl = "https://example.com/icons/child.png",
                ImageUrl = "https://example.com/images/child.jpg"
            };

            // Act
            var result = await _categoryService.CreateCategoryAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(request.Name, result.Name);
            Assert.Equal(parentCategory.Id, result.ParentCategoryId);
        }

        [Fact]
        public async Task GetCategoriesWithSubcategories_ReturnsHierarchicalCategories()
        {
            // Arrange
            var parent = new Category
            {
                Name = "Parent",
                Description = "Parent Category",
                IconUrl = "https://example.com/icons/parent.png",
                ImageUrl = "https://example.com/images/parent.jpg"
            };
            _dbContext.Categories.Add(parent);
            await _dbContext.SaveChangesAsync();

            var child1 = new Category
            { 
                Name = "Child1", 
                Description = "First Child",
                IconUrl = "https://example.com/icons/child1.png",
                ImageUrl = "https://example.com/images/child1.jpg",
                ParentCategoryId = parent.Id 
            };
            var child2 = new Category 
            { 
                Name = "Child2", 
                Description = "Second Child",
                IconUrl = "https://example.com/icons/child2.png",
                ImageUrl = "https://example.com/images/child2.jpg",
                ParentCategoryId = parent.Id 
            };
            _dbContext.Categories.AddRange(child1, child2);
            await _dbContext.SaveChangesAsync();

            var grandchild = new Category 
            { 
                Name = "Grandchild", 
                Description = "Grandchild Category",
                IconUrl = "https://example.com/icons/grandchild.png",
                ImageUrl = "https://example.com/images/grandchild.jpg",
                ParentCategoryId = child1.Id 
            };
            _dbContext.Categories.Add(grandchild);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _categoryService.GetAllCategoriesAsync();

            // Assert
            Assert.NotNull(result);
            var rootCategories = result.Where(c => !c.ParentCategoryId.HasValue).ToList();
            Assert.Single(rootCategories);
            
            var parentDto = rootCategories.First();
            Assert.Equal(parent.Name, parentDto.Name);
            
            // Vérifier que les enfants sont correctement récupérés
            var childCategories = result.Where(c => c.ParentCategoryId == parent.Id).ToList();
            Assert.Equal(2, childCategories.Count);
        }

        [Fact]
        public async Task UpdateCategory_WithValidData_ReturnsUpdatedCategoryDto()
        {
            // Arrange
            var category = new Category
            {
                Name = "Test Category",
                Description = "Test Description",
                IconUrl = "https://example.com/icons/test.png",
                ImageUrl = "https://example.com/images/test.jpg"
            };
            _dbContext.Categories.Add(category);
            await _dbContext.SaveChangesAsync();

            var updateCategoryRequest = new UpdateCategoryRequest
            {
                Name = "Updated Category",
                Description = "Updated Description",
                IconUrl = "https://example.com/icons/updated.png",
                ImageUrl = "https://example.com/images/updated.jpg"
            };

            // Act
            var result = await _categoryService.UpdateCategoryAsync(category.Id, updateCategoryRequest);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(updateCategoryRequest.Name, result.Name);
            Assert.Equal(updateCategoryRequest.Description, result.Description);
        }

        [Fact]
        public async Task DeleteCategory_WithValidId_ReturnsTrue()
        {
            // Arrange
            var category = new Category
            {
                Name = "Test Category",
                Description = "Test Description",
                IconUrl = "https://example.com/icons/test.png",
                ImageUrl = "https://example.com/images/test.jpg"
            };
            _dbContext.Categories.Add(category);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _categoryService.DeleteCategoryAsync(category.Id);

            // Assert
            Assert.True(result);
            Assert.Null(await _dbContext.Categories.FindAsync(category.Id));
        }

        [Fact]
        public async Task GetCategoryWithSubcategories_ReturnsCorrectHierarchy()
        {
            // Arrange
            var parent = new Category
            {
                Name = "Parent Category",
                Description = "Parent Description",
                IconUrl = "https://example.com/icons/parent.png",
                ImageUrl = "https://example.com/images/parent.jpg"
            };
            _dbContext.Categories.Add(parent);
            await _dbContext.SaveChangesAsync();

            var child = new Category
            {
                Name = "Child Category",
                Description = "Child Description",
                ParentCategoryId = parent.Id,
                IconUrl = "https://example.com/icons/child.png",
                ImageUrl = "https://example.com/images/child.jpg"
            };
            _dbContext.Categories.Add(child);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _categoryService.GetCategoryByIdAsync(parent.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(parent.Name, result.Name);
        }
    }
}
