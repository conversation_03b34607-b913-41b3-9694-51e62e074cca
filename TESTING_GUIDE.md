# 🧪 Guide de Test - NafaPlace avec Docker

## 🚀 Déploiement Initial

### 1. <PERSON><PERSON> le déploiement
```bash
# Windows
.\scripts\deploy-docker.ps1

# Linux/Mac
chmod +x scripts/deploy-docker.sh
./scripts/deploy-docker.sh
```

### 2. Vérifier que tous les services sont actifs
```bash
docker-compose ps
```

Tous les services doivent être "Up" :
- ✅ api-gateway (port 5000)
- ✅ identity-api (port 5155)
- ✅ catalog-api (port 5243)
- ✅ cart-api (port 5003)
- ✅ order-api (port 5004)
- ✅ payment-api (port 5005)
- ✅ reviews-api (port 5006)
- ✅ notifications-api (port 5007)

## 🔍 Tests des APIs

### 1. API Gateway (Port 5000)
```bash
# Test de santé
curl http://localhost:5000/health

# Swagger UI
# Ouvrir: http://localhost:5000/swagger
```

### 2. Identity API (Port 5155)
```bash
# Créer un utilisateur
curl -X POST http://localhost:5155/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!",
    "firstName": "Test",
    "lastName": "User"
  }'

# Se connecter
curl -X POST http://localhost:5155/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!"
  }'
```

### 3. Catalog API (Port 5243)
```bash
# Lister les produits
curl http://localhost:5243/api/products

# Rechercher des produits
curl "http://localhost:5243/api/products/search?query=samsung"

# Obtenir un produit spécifique
curl http://localhost:5243/api/products/1
```

### 4. Cart API (Port 5003)
```bash
# Obtenir le panier d'un utilisateur
curl http://localhost:5003/api/cart/user123

# Ajouter un produit au panier
curl -X POST http://localhost:5003/api/cart/user123/items \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 1,
    "quantity": 2
  }'

# Obtenir le résumé du panier
curl http://localhost:5003/api/cart/user123/summary
```

### 5. Reviews API (Port 5006)
```bash
# Obtenir les avis d'un produit
curl http://localhost:5006/api/reviews/product/1

# Obtenir le résumé des avis
curl http://localhost:5006/api/reviews/product/1/summary

# Créer un avis (nécessite authentification)
curl -X POST http://localhost:5006/api/reviews \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "productId": 1,
    "rating": 5,
    "title": "Excellent produit!",
    "comment": "Très satisfait de cet achat, livraison rapide."
  }'
```

### 6. Notifications API (Port 5007)
```bash
# Obtenir les notifications (nécessite authentification)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:5007/api/notifications

# Obtenir les statistiques
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:5007/api/notifications/stats
```

## 💰 Tests Spécifiques GNF

### 1. Vérifier les Prix en GNF
```bash
# Vérifier qu'un produit affiche le prix en GNF
curl http://localhost:5243/api/products/1 | jq '.price, .currency'
# Résultat attendu: prix en GNF (ex: 2500000, "GNF")
```

### 2. Test du Panier avec Calculs GNF
```bash
# Ajouter des produits et vérifier les calculs
curl http://localhost:5003/api/cart/user123/summary
# Vérifier:
# - currency: "GNF"
# - shippingFee: 25000 (si sous-total < 500000)
# - shippingFee: 0 (si sous-total >= 500000)
# - tax: 18% du sous-total
```

### 3. Test de Livraison Gratuite
```bash
# Ajouter des produits pour un total > 500,000 GNF
curl -X POST http://localhost:5003/api/cart/user123/items \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 2,
    "quantity": 1
  }'

# Vérifier que shippingFee = 0
curl http://localhost:5003/api/cart/user123/summary
```

## 🌐 Tests des Applications Web

### 1. Web App (Port 8080)
```bash
# Ouvrir dans le navigateur
http://localhost:8080

# Vérifier:
# - Affichage des prix en GNF
# - Fonctionnement du panier
# - Calculs corrects
```

### 2. Admin Portal (Port 8081)
```bash
# Ouvrir dans le navigateur
http://localhost:8081

# Tester:
# - Gestion des produits
# - Affichage des commandes
# - Statistiques en GNF
```

### 3. Seller Portal (Port 8082)
```bash
# Ouvrir dans le navigateur
http://localhost:8082

# Vérifier:
# - Ajout de produits avec prix GNF
# - Notifications de ventes
# - Statistiques vendeur
```

## 💳 Tests de Paiement Stripe

### 1. Configuration Stripe
Vérifier que les clés Stripe sont configurées pour GNF :
```bash
curl http://localhost:5005/api/payments/config
```

### 2. Créer une Session de Paiement
```bash
curl -X POST http://localhost:5005/api/payments/create-checkout-session \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "currency": "gnf",
    "items": [{
      "name": "Test Product",
      "unitAmount": 250000000,
      "quantity": 1
    }],
    "successUrl": "http://localhost:8080/success",
    "cancelUrl": "http://localhost:8080/cancel"
  }'
```

## 📊 Tests de Base de Données

### 1. Connexion PostgreSQL
```bash
# Catalog DB
docker exec -it nafaplace-catalog-db psql -U postgres -d NafaPlace.Catalog

# Reviews DB
docker exec -it nafaplace-reviews-db psql -U postgres -d NafaPlace.Reviews

# Notifications DB
docker exec -it nafaplace-notifications-db psql -U postgres -d NafaPlace.Notifications
```

### 2. Vérifier les Données GNF
```sql
-- Dans Catalog DB
SELECT "Name", "Price", "Currency" FROM "Products" LIMIT 5;

-- Dans Reviews DB
SELECT COUNT(*) FROM "Reviews";

-- Dans Notifications DB
SELECT COUNT(*) FROM "Notifications";
```

## 🔧 Dépannage

### 1. Voir les Logs
```bash
# Tous les services
docker-compose logs -f

# Service spécifique
docker-compose logs -f api-gateway
docker-compose logs -f catalog-api
docker-compose logs -f reviews-api
```

### 2. Redémarrer un Service
```bash
docker-compose restart api-gateway
```

### 3. Reconstruire un Service
```bash
docker-compose up --build -d api-gateway
```

### 4. Nettoyer et Redémarrer
```bash
docker-compose down -v
docker-compose up --build -d
```

## ✅ Checklist de Validation

### Fonctionnalités de Base
- [ ] Tous les services démarrent sans erreur
- [ ] API Gateway route correctement vers tous les services
- [ ] Authentification JWT fonctionne
- [ ] Swagger UI accessible sur tous les services

### Fonctionnalités GNF
- [ ] Produits affichent les prix en GNF
- [ ] Calculs de panier corrects (sous-total, TVA, livraison)
- [ ] Livraison gratuite à 500,000 GNF
- [ ] Frais de livraison à 25,000 GNF
- [ ] Paiements Stripe en GNF

### Nouvelles Fonctionnalités
- [ ] Service Reviews fonctionne
- [ ] Service Notifications fonctionne
- [ ] Avis et évaluations
- [ ] Notifications en temps réel

### Applications Web
- [ ] Web App accessible et fonctionnelle
- [ ] Admin Portal accessible
- [ ] Seller Portal accessible
- [ ] Affichage correct des prix GNF

## 🎯 Scénarios de Test Complets

### Scénario 1: Parcours Client Complet
1. Créer un compte utilisateur
2. Parcourir les produits
3. Ajouter des produits au panier
4. Vérifier les calculs GNF
5. Procéder au checkout
6. Effectuer un paiement test

### Scénario 2: Parcours Vendeur
1. Créer un compte vendeur
2. Ajouter des produits avec prix GNF
3. Recevoir des notifications de ventes
4. Consulter les statistiques

### Scénario 3: Administration
1. Accéder au portail admin
2. Gérer les produits et prix
3. Consulter les commandes
4. Gérer les utilisateurs

La plateforme est maintenant prête pour des tests complets ! 🚀
