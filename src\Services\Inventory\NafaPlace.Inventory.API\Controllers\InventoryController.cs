using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Inventory.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class InventoryController : ControllerBase
{
    private readonly IInventoryService _inventoryService;
    private readonly ILogger<InventoryController> _logger;

    public InventoryController(IInventoryService inventoryService, ILogger<InventoryController> logger)
    {
        _inventoryService = inventoryService;
        _logger = logger;
    }

    // Stock Validation
    [HttpGet("products/{productId}/validate")]
    public async Task<ActionResult<StockValidationResult>> ValidateStock(int productId, [FromQuery] int quantity)
    {
        try
        {
            var result = await _inventoryService.ValidateStockAvailabilityAsync(productId, quantity);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating stock for product {ProductId}", productId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("products/{productId}/available")]
    public async Task<ActionResult<int>> GetAvailableStock(int productId)
    {
        try
        {
            var availableStock = await _inventoryService.GetAvailableStockAsync(productId);
            return Ok(availableStock);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available stock for product {ProductId}", productId);
            return StatusCode(500, "Internal server error");
        }
    }

    // Stock Reservations
    [HttpPost("reservations")]
    [Authorize]
    public async Task<ActionResult<StockReservationDto>> CreateReservation([FromBody] CreateReservationRequest request)
    {
        try
        {
            var userId = GetUserId();
            request.UserId = userId;
            
            var reservation = await _inventoryService.CreateReservationAsync(request);
            return CreatedAtAction(nameof(GetReservation), new { id = reservation.Id }, reservation);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating stock reservation");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("reservations/{id}")]
    [Authorize]
    public async Task<ActionResult<StockReservationDto>> GetReservation(int id)
    {
        try
        {
            var reservation = await _inventoryService.GetReservationAsync(id);
            if (reservation == null)
            {
                return NotFound("Reservation not found");
            }
            return Ok(reservation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting reservation {ReservationId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("reservations/{id}/confirm")]
    [Authorize]
    public async Task<ActionResult> ConfirmReservation(int id, [FromBody] ConfirmReservationRequest request)
    {
        try
        {
            var success = await _inventoryService.ConfirmReservationAsync(id, request.OrderId);
            if (!success)
            {
                return NotFound("Reservation not found or cannot be confirmed");
            }
            return Ok(new { message = "Reservation confirmed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming reservation {ReservationId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("reservations/{id}/release")]
    [Authorize]
    public async Task<ActionResult> ReleaseReservation(int id, [FromBody] ReleaseReservationRequest request)
    {
        try
        {
            var success = await _inventoryService.ReleaseReservationAsync(id, request.Reason);
            if (!success)
            {
                return NotFound("Reservation not found or cannot be released");
            }
            return Ok(new { message = "Reservation released successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing reservation {ReservationId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("reservations/user")]
    [Authorize]
    public async Task<ActionResult<List<StockReservationDto>>> GetUserReservations()
    {
        try
        {
            var userId = GetUserId();
            var reservations = await _inventoryService.GetUserReservationsAsync(userId);
            return Ok(reservations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user reservations");
            return StatusCode(500, "Internal server error");
        }
    }

    // Stock Management
    [HttpPut("products/{productId}/stock")]
    [Authorize(Roles = "Admin,Seller")]
    public async Task<ActionResult> UpdateStock(int productId, [FromBody] UpdateStockRequest request)
    {
        try
        {
            var userId = GetUserId();
            var success = await _inventoryService.UpdateStockAsync(productId, request.NewQuantity, request.Reason, userId);
            
            if (!success)
            {
                return BadRequest("Failed to update stock");
            }
            
            return Ok(new { message = "Stock updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating stock for product {ProductId}", productId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("products/{productId}/adjust")]
    [Authorize(Roles = "Admin,Seller")]
    public async Task<ActionResult> AdjustStock(int productId, [FromBody] StockAdjustmentRequest request)
    {
        try
        {
            var userId = GetUserId();
            request.ProductId = productId;
            
            var success = await _inventoryService.AdjustStockAsync(request, userId);
            if (!success)
            {
                return BadRequest("Failed to adjust stock");
            }
            
            return Ok(new { message = "Stock adjusted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adjusting stock for product {ProductId}", productId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("bulk-update")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> BulkUpdateStock([FromBody] BulkStockUpdateRequest request)
    {
        try
        {
            var userId = GetUserId();
            var success = await _inventoryService.BulkUpdateStockAsync(request, userId);
            
            if (!success)
            {
                return BadRequest("Some stock updates failed");
            }
            
            return Ok(new { message = "Bulk stock update completed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing bulk stock update");
            return StatusCode(500, "Internal server error");
        }
    }

    // Stock Alerts
    [HttpGet("alerts")]
    [Authorize(Roles = "Admin,Seller")]
    public async Task<ActionResult<List<StockAlertDto>>> GetAlerts([FromQuery] int? sellerId = null)
    {
        try
        {
            var alerts = await _inventoryService.GetActiveAlertsAsync(sellerId);
            return Ok(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting stock alerts");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("alerts/{id}/acknowledge")]
    [Authorize(Roles = "Admin,Seller")]
    public async Task<ActionResult> AcknowledgeAlert(int id)
    {
        try
        {
            var userId = GetUserId();
            var success = await _inventoryService.AcknowledgeAlertAsync(id, userId);
            
            if (!success)
            {
                return NotFound("Alert not found");
            }
            
            return Ok(new { message = "Alert acknowledged successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging alert {AlertId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    // Stock Movements
    [HttpGet("products/{productId}/movements")]
    [Authorize(Roles = "Admin,Seller")]
    public async Task<ActionResult<List<StockMovementDto>>> GetProductMovements(int productId, [FromQuery] int page = 1, [FromQuery] int pageSize = 20)
    {
        try
        {
            var movements = await _inventoryService.GetProductMovementsAsync(productId, page, pageSize);
            return Ok(movements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product movements for {ProductId}", productId);
            return StatusCode(500, "Internal server error");
        }
    }

    // Dashboard
    [HttpGet("dashboard")]
    [AllowAnonymous] // Temporairement pour les tests
    public async Task<ActionResult<InventoryDashboardDto>> GetDashboard([FromQuery] int? sellerId = null)
    {
        try
        {
            var dashboard = await _inventoryService.GetInventoryDashboardAsync(sellerId);
            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inventory dashboard");
            return StatusCode(500, "Internal server error");
        }
    }

    // Maintenance
    [HttpPost("maintenance/cleanup-expired")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> CleanupExpiredReservations()
    {
        try
        {
            var count = await _inventoryService.CleanupExpiredReservationsAsync();
            return Ok(new { message = $"Cleaned up {count} expired reservations" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired reservations");
            return StatusCode(500, "Internal server error");
        }
    }

    private string GetUserId()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            throw new UnauthorizedAccessException("User ID not found in token");
        }
        return userId;
    }
}

// Request DTOs
public class ConfirmReservationRequest
{
    public string OrderId { get; set; } = string.Empty;
}

public class ReleaseReservationRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class UpdateStockRequest
{
    public int NewQuantity { get; set; }
    public string Reason { get; set; } = string.Empty;
}
