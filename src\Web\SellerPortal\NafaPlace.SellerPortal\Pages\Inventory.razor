@page "/inventory"
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using NafaPlace.SellerPortal.Services
@using NafaPlace.SellerPortal.Models.Inventory
@attribute [Authorize]
@inject InventoryService InventoryService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService
@inject ILocalStorageService LocalStorage

<h1 class="visually-hidden">Gestion des Stocks - NafaPlace Vendeur</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Stocks</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Inventaire</li>
    </ol>

    @if (_isLoading)
    {
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else
    {
        <!-- Statistiques du vendeur -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card bg-primary text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Mes Produits</h6>
                                <h3 class="mb-0">@_dashboard?.TotalProducts</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-box-seam fs-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-white stretched-link" href="/products">Voir les détails</a>
                        <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card bg-warning text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Stock Faible</h6>
                                <h3 class="mb-0">@_dashboard?.LowStockCount</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-exclamation-triangle fs-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <span class="small text-white">Nécessite attention</span>
                        <div class="small text-white"><i class="bi bi-exclamation-triangle"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card bg-danger text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Rupture de Stock</h6>
                                <h3 class="mb-0">@_dashboard?.OutOfStockProducts</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-x-circle fs-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <span class="small text-white">Action requise</span>
                        <div class="small text-white"><i class="bi bi-x-circle"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card bg-success text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Valeur Stock</h6>
                                <h3 class="mb-0">@(_dashboard?.TotalInventoryValue.ToString("N0")) GNF</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-currency-exchange fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alertes récentes -->
        @if (_dashboard?.RecentAlerts?.Any() == true)
        {
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-bell me-1"></i>
                        Mes Alertes (@_dashboard.RecentAlerts.Count(a => !a.IsAcknowledged))
                    </div>
                    <button class="btn btn-sm btn-outline-primary" @onclick="LoadDashboard">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        Actualiser
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Type</th>
                                    <th>Stock Actuel</th>
                                    <th>Seuil</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var alert in _dashboard.RecentAlerts.Take(5))
                                {
                                    <tr class="@GetAlertRowClass(alert.Severity)">
                                        <td>@alert.ProductName</td>
                                        <td>
                                            <span class="badge @GetAlertBadgeClass(alert.Type)">
                                                @GetAlertTypeText(alert.Type)
                                            </span>
                                        </td>
                                        <td>@alert.CurrentStock</td>
                                        <td>@alert.ThresholdValue</td>
                                        <td>@alert.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                        <td>
                                            @if (!alert.IsAcknowledged)
                                            {
                                                <button class="btn btn-sm btn-outline-success" @onclick="() => AcknowledgeAlert(alert.Id)">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                            }
                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => NavigateToProduct(alert.ProductId)">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }

        <!-- Produits en stock faible -->
        @if (_dashboard?.LowStockProducts?.Any() == true)
        {
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    Produits Nécessitant un Réapprovisionnement
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Stock Actuel</th>
                                    <th>Quantité Vendue</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var product in _dashboard.LowStockProducts.Take(10))
                                {
                                    <tr class="table-warning">
                                        <td>
                                            <strong>@product.ProductName</strong>
                                            <br />
                                            <small class="text-muted">ID: @product.ProductId</small>
                                        </td>
                                        <td>
                                            <span class="text-warning fw-bold">@product.CurrentStock</span>
                                        </td>
                                        <td>@product.SoldQuantity</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary me-2" @onclick="() => ShowUpdateStockModal(product.ProductId, product.ProductName, product.CurrentStock)">
                                                <i class="bi bi-plus-circle me-1"></i>
                                                Réapprovisionner
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" @onclick="() => NavigateToProduct(product.ProductId)">
                                                <i class="bi bi-pencil me-1"></i>
                                                Modifier
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }

        <!-- Actions rapides -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-lightning me-1"></i>
                        Actions Rapides
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <button class="btn btn-outline-primary w-100 mb-2" @onclick="NavigateToProducts">
                                    <i class="bi bi-box me-1"></i>
                                    Gérer mes Produits
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-success w-100 mb-2" @onclick="ShowBulkUpdateModal">
                                    <i class="bi bi-arrow-up-circle me-1"></i>
                                    Mise à Jour en Lot
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-info w-100 mb-2" @onclick="NavigateToOrders">
                                    <i class="bi bi-cart me-1"></i>
                                    Voir les Commandes
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<!-- Modal de mise à jour du stock -->
@if (_showUpdateStockModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Mettre à jour le stock</h5>
                    <button type="button" class="btn-close" @onclick="HideUpdateStockModal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Produit</label>
                        <input type="text" class="form-control" value="@_selectedProductName" readonly />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Stock actuel</label>
                        <input type="number" class="form-control" value="@_currentStock" readonly />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Nouveau stock</label>
                        <input type="number" class="form-control" @bind="_newStock" min="0" />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Raison</label>
                        <select class="form-select" @bind="_updateReason">
                            <option value="">Sélectionner une raison</option>
                            <option value="Réapprovisionnement">Réapprovisionnement</option>
                            <option value="Correction d'inventaire">Correction d'inventaire</option>
                            <option value="Retour client">Retour client</option>
                            <option value="Produit endommagé">Produit endommagé</option>
                            <option value="Autre">Autre</option>
                        </select>
                    </div>
                    @if (_updateReason == "Autre")
                    {
                        <div class="mb-3">
                            <label class="form-label">Préciser</label>
                            <input type="text" class="form-control" @bind="_customReason" placeholder="Préciser la raison..." />
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="HideUpdateStockModal">Annuler</button>
                    <button type="button" class="btn btn-primary" @onclick="UpdateStock" disabled="@(_isUpdatingStock || string.IsNullOrEmpty(_updateReason))">
                        @if (_isUpdatingStock)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        Mettre à jour
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private InventoryDashboardDto? _dashboard;
    private bool _isLoading = true;
    private int _sellerId;

    // Modal de mise à jour du stock
    private bool _showUpdateStockModal = false;
    private bool _isUpdatingStock = false;
    private int _selectedProductId;
    private string _selectedProductName = "";
    private int _currentStock;
    private int _newStock;
    private string _updateReason = "";
    private string _customReason = "";

    protected override async Task OnInitializedAsync()
    {
        await GetSellerInfo();
        await LoadDashboard();
    }

    private async Task GetSellerInfo()
    {
        try
        {
            var userInfo = await LocalStorage.GetItemAsync<object>("userInfo");
            if (userInfo != null)
            {
                var userInfoJson = userInfo.ToString();
                var userInfoDict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(userInfoJson);
                if (userInfoDict != null && userInfoDict.ContainsKey("sellerId"))
                {
                    _sellerId = Convert.ToInt32(userInfoDict["sellerId"]);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des informations du vendeur: {ex.Message}");
            _sellerId = 1; // Valeur par défaut pour les tests
        }
    }

    private async Task LoadDashboard()
    {
        _isLoading = true;
        try
        {
            _dashboard = await InventoryService.GetInventoryDashboardAsync(_sellerId);
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement du tableau de bord: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task AcknowledgeAlert(int alertId)
    {
        try
        {
            await InventoryService.AcknowledgeAlertAsync(alertId);
            await LoadDashboard();
            NotificationService.Success("Alerte acquittée avec succès");
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors de l'acquittement: {ex.Message}");
        }
    }

    private void NavigateToProduct(int productId)
    {
        NavigationManager.NavigateTo($"/products/edit/{productId}");
    }

    private void ShowUpdateStockModal(int productId, string productName, int currentStock)
    {
        _selectedProductId = productId;
        _selectedProductName = productName;
        _currentStock = currentStock;
        _newStock = currentStock;
        _updateReason = "";
        _customReason = "";
        _showUpdateStockModal = true;
        StateHasChanged();
    }

    private void HideUpdateStockModal()
    {
        _showUpdateStockModal = false;
        StateHasChanged();
    }

    private async Task UpdateStock()
    {
        if (string.IsNullOrEmpty(_updateReason)) return;

        _isUpdatingStock = true;
        try
        {
            var reason = _updateReason == "Autre" ? _customReason : _updateReason;
            var request = new UpdateStockRequest
            {
                NewQuantity = _newStock,
                Reason = reason,
                Notes = $"Mise à jour depuis le portail vendeur - Stock précédent: {_currentStock}"
            };

            var success = await InventoryService.UpdateStockAsync(_selectedProductId, request);
            if (success)
            {
                HideUpdateStockModal();
                await LoadDashboard();
                NotificationService.Success("Stock mis à jour avec succès");
            }
            else
            {
                NotificationService.Error("Erreur lors de la mise à jour du stock");
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur: {ex.Message}");
        }
        finally
        {
            _isUpdatingStock = false;
            StateHasChanged();
        }
    }

    private void ShowBulkUpdateModal()
    {
        NotificationService.Info("Fonctionnalité de mise à jour en lot à venir");
    }

    // Méthodes d'aide pour les styles
    private string GetAlertRowClass(AlertSeverity severity)
    {
        return severity switch
        {
            AlertSeverity.Critical => "table-danger",
            AlertSeverity.Warning => "table-warning",
            AlertSeverity.Emergency => "table-danger",
            _ => ""
        };
    }

    private string GetAlertBadgeClass(AlertType type)
    {
        return type switch
        {
            AlertType.OutOfStock => "bg-danger",
            AlertType.LowStock => "bg-warning",
            AlertType.OverStock => "bg-info",
            _ => "bg-secondary"
        };
    }

    private string GetAlertTypeText(AlertType type)
    {
        return type switch
        {
            AlertType.OutOfStock => "Rupture",
            AlertType.LowStock => "Stock Faible",
            AlertType.OverStock => "Surstock",
            AlertType.StockMovement => "Mouvement",
            AlertType.ReservationExpiry => "Réservation",
            _ => type.ToString()
        };
    }

    private void NavigateToProducts()
    {
        NavigationManager.NavigateTo("/products");
    }

    private void NavigateToOrders()
    {
        NavigationManager.NavigateTo("/orders");
    }
}
