using Microsoft.Extensions.Configuration;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Services;
using Xunit;

namespace NafaPlace.Identity.UnitTests.Services;

public class JwtServiceTests
{
    private readonly IConfiguration _configuration;
    private readonly JwtService _jwtService;
    private readonly User _testUser;

    public JwtServiceTests()
    {
        // Configuration pour les tests
        var inMemorySettings = new Dictionary<string, string>
        {
            {"JwtSettings:Secret", "NafaPlaceSecretKey2025ForProductionEnvironment"},
            {"JwtSettings:Issuer", "NafaPlace"},
            {"JwtSettings:Audience", "NafaPlaceApi"},
            {"JwtSettings:TokenLifetimeMinutes", "60"}
        };

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(inMemorySettings)
            .Build();

        _jwtService = new JwtService(_configuration);

        _testUser = new User
        {
            Email = "<EMAIL>",
            Username = "testuser"
        };
    }

    [Fact]
    public void GenerateAccessToken_ShouldReturnValidToken()
    {
        // Arrange
        var roles = new[] { "User" };

        // Act
        var token = _jwtService.GenerateAccessToken(_testUser, roles);

        // Assert
        Assert.NotNull(token);
        Assert.True(_jwtService.ValidateToken(token));
    }

    [Fact]
    public void GenerateRefreshToken_ShouldReturnUniqueTokens()
    {
        // Act
        var token1 = _jwtService.GenerateRefreshToken();
        var token2 = _jwtService.GenerateRefreshToken();

        // Assert
        Assert.NotNull(token1);
        Assert.NotNull(token2);
        Assert.NotEqual(token1, token2);
    }

    [Fact]
    public void ValidateToken_WithValidToken_ShouldReturnTrue()
    {
        // Arrange
        var token = _jwtService.GenerateAccessToken(_testUser, new[] { "User" });

        // Act
        var isValid = _jwtService.ValidateToken(token);

        // Assert
        Assert.True(isValid);
    }

    [Fact]
    public void ValidateToken_WithInvalidToken_ShouldReturnFalse()
    {
        // Act
        var isValid = _jwtService.ValidateToken("invalid-token");

        // Assert
        Assert.False(isValid);
    }

    [Fact]
    public void GetUserIdFromToken_WithValidToken_ShouldReturnUserId()
    {
        // Arrange
        var token = _jwtService.GenerateAccessToken(_testUser, new[] { "User" });

        // Act
        var userId = _jwtService.GetUserIdFromToken(token);

        // Assert
        Assert.NotNull(userId);
        Assert.Equal(_testUser.Id.ToString(), userId);
    }

    [Fact]
    public void GetUserIdFromToken_WithInvalidToken_ShouldReturnNull()
    {
        // Act
        var userId = _jwtService.GetUserIdFromToken("invalid-token");

        // Assert
        Assert.Null(userId);
    }
}
