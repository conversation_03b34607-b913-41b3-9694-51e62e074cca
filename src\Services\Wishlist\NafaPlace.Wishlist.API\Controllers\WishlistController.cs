using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Wishlist.Application.DTOs;
using NafaPlace.Wishlist.Application.DTOs.Requests;
using NafaPlace.Wishlist.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Wishlist.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WishlistController : ControllerBase
{
    private readonly IWishlistService _wishlistService;
    private readonly ILogger<WishlistController> _logger;

    public WishlistController(IWishlistService wishlistService, ILogger<WishlistController> logger)
    {
        _wishlistService = wishlistService;
        _logger = logger;
    }

    [HttpGet]
    [Authorize]
    public async Task<ActionResult<WishlistDto>> GetUserWishlist()
    {
        try
        {
            var userId = GetUserId();
            var wishlist = await _wishlistService.GetUserWishlistAsync(userId);
            return Ok(wishlist);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user wishlist");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("summary")]
    [Authorize]
    public async Task<ActionResult<WishlistSummaryDto>> GetWishlistSummary()
    {
        try
        {
            var userId = GetUserId();
            var wishlist = await _wishlistService.GetUserWishlistAsync(userId);
            
            var summary = new WishlistSummaryDto
            {
                Id = wishlist.Id,
                UserId = wishlist.UserId,
                Name = wishlist.Name,
                ItemCount = wishlist.ItemCount,
                TotalValue = wishlist.TotalValue,
                Currency = wishlist.Currency,
                LastUpdated = wishlist.LastUpdated
            };

            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist summary");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("count")]
    [Authorize]
    public async Task<ActionResult<int>> GetWishlistItemCount()
    {
        try
        {
            var userId = GetUserId();
            var count = await _wishlistService.GetWishlistItemCountAsync(userId);
            return Ok(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist item count");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("items")]
    [Authorize]
    public async Task<ActionResult<List<WishlistItemDto>>> GetWishlistItems([FromQuery] int page = 1, [FromQuery] int pageSize = 20)
    {
        try
        {
            var userId = GetUserId();
            var items = await _wishlistService.GetWishlistItemsAsync(userId, page, pageSize);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist items");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("items")]
    [Authorize]
    public async Task<ActionResult<WishlistItemDto>> AddToWishlist([FromBody] AddToWishlistRequest request)
    {
        try
        {
            var userId = GetUserId();
            var item = await _wishlistService.AddToWishlistAsync(userId, request);
            return CreatedAtAction(nameof(GetWishlistItems), new { }, item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding item to wishlist");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("items/{productId}")]
    [Authorize]
    public async Task<ActionResult> RemoveFromWishlist(int productId)
    {
        try
        {
            var userId = GetUserId();
            var success = await _wishlistService.RemoveFromWishlistAsync(userId, productId);
            
            if (!success)
            {
                return NotFound("Item not found in wishlist");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing item from wishlist");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("items/{productId}/exists")]
    [Authorize]
    public async Task<ActionResult<bool>> IsProductInWishlist(int productId)
    {
        try
        {
            var userId = GetUserId();
            var exists = await _wishlistService.IsProductInWishlistAsync(userId, productId);
            return Ok(exists);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if product is in wishlist");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("clear")]
    [Authorize]
    public async Task<ActionResult> ClearWishlist()
    {
        try
        {
            var userId = GetUserId();
            var success = await _wishlistService.ClearWishlistAsync(userId);
            
            if (!success)
            {
                return BadRequest("Failed to clear wishlist");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing wishlist");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("items/{productId}/move-to-cart")]
    [Authorize]
    public async Task<ActionResult> MoveToCart(int productId)
    {
        try
        {
            var userId = GetUserId();
            var success = await _wishlistService.MoveToCartAsync(userId, productId);
            
            if (!success)
            {
                return NotFound("Item not found in wishlist");
            }

            return Ok(new { message = "Item moved to cart successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving item to cart");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("recent")]
    [Authorize]
    public async Task<ActionResult<List<WishlistItemDto>>> GetRecentlyAddedItems([FromQuery] int count = 5)
    {
        try
        {
            var userId = GetUserId();
            var items = await _wishlistService.GetRecentlyAddedItemsAsync(userId, count);
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recently added items");
            return StatusCode(500, "Internal server error");
        }
    }

    // Public endpoint for guest users (using session/localStorage)
    [HttpGet("guest/{guestId}")]
    [AllowAnonymous]
    public async Task<ActionResult<WishlistDto>> GetGuestWishlist(string guestId)
    {
        try
        {
            var wishlist = await _wishlistService.GetUserWishlistAsync($"guest_{guestId}");
            return Ok(wishlist);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting guest wishlist");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("guest/{guestId}/items")]
    [AllowAnonymous]
    public async Task<ActionResult<WishlistItemDto>> AddToGuestWishlist(string guestId, [FromBody] AddToWishlistRequest request)
    {
        try
        {
            var item = await _wishlistService.AddToWishlistAsync($"guest_{guestId}", request);
            return CreatedAtAction(nameof(GetGuestWishlist), new { guestId }, item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding item to guest wishlist");
            return StatusCode(500, "Internal server error");
        }
    }

    private string GetUserId()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            throw new UnauthorizedAccessException("User ID not found in token");
        }
        return userId;
    }
}
