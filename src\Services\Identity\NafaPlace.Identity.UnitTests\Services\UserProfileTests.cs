using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.DTOs;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Data;
using NafaPlace.Identity.Infrastructure.Services;
using Xunit;

namespace NafaPlace.Identity.UnitTests.Services;

public class UserProfileTests
{
    private readonly DbContextOptions<IdentityDbContext> _dbContextOptions;

    public UserProfileTests()
    {
        _dbContextOptions = new DbContextOptionsBuilder<IdentityDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
    }

    [Fact]
    public async Task GetUserProfileAsync_WithValidId_ShouldReturnUserProfile()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User
        {
            Id = userId,
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = "hash",
            PhoneNumber = "1234567890",
            FirstName = "Test",
            LastName = "User",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        await context.SaveChangesAsync();

        var userService = new UserService(context);

        // Act
        var result = await userService.GetUserProfileAsync(userId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(user.Id, result.Id);
        Assert.Equal(user.Email, result.Email);
        Assert.Equal(user.Username, result.Username);
        Assert.Equal(user.FirstName, result.FirstName);
        Assert.Equal(user.LastName, result.LastName);
        Assert.Equal(user.PhoneNumber, result.PhoneNumber);
    }

    [Fact]
    public async Task GetUserProfileAsync_WithInvalidId_ShouldThrowNotFoundException()
    {
        // Arrange
        var invalidUserId = Guid.NewGuid();

        await using var context = new IdentityDbContext(_dbContextOptions);
        var userService = new UserService(context);

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => userService.GetUserProfileAsync(invalidUserId));
    }

    [Fact]
    public async Task UpdateUserProfileAsync_WithValidRequest_ShouldUpdateProfile()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = new User
        {
            Id = userId,
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = "hash",
            PhoneNumber = "1234567890",
            FirstName = "Test",
            LastName = "User",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        await context.SaveChangesAsync();

        var request = new UpdateUserProfileRequest
        {
            FirstName = "Updated",
            LastName = "Name",
            PhoneNumber = "9876543210"
        };

        var userService = new UserService(context);

        // Act
        var result = await userService.UpdateUserProfileAsync(userId, request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(userId, result.Id);
        Assert.Equal(request.FirstName, result.FirstName);
        Assert.Equal(request.LastName, result.LastName);
        Assert.Equal(request.PhoneNumber, result.PhoneNumber);
        Assert.Equal(user.Email, result.Email); // Email shouldn't change

        // Verify in database
        var updatedUser = await context.Users.FindAsync(userId);
        Assert.NotNull(updatedUser);
        Assert.Equal(request.FirstName, updatedUser.FirstName);
        Assert.Equal(request.LastName, updatedUser.LastName);
        Assert.Equal(request.PhoneNumber, updatedUser.PhoneNumber);
    }

    [Fact]
    public async Task UpdateUserProfileAsync_WithInvalidId_ShouldThrowNotFoundException()
    {
        // Arrange
        var invalidUserId = Guid.NewGuid();
        var request = new UpdateUserProfileRequest
        {
            FirstName = "Updated",
            LastName = "Name",
            PhoneNumber = "9876543210"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        var userService = new UserService(context);

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => userService.UpdateUserProfileAsync(invalidUserId, request));
    }

    [Fact]
    public async Task ChangePasswordAsync_WithValidRequest_ShouldUpdatePassword()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var currentPasswordHash = BCrypt.Net.BCrypt.HashPassword("CurrentPassword123!");
        var user = new User
        {
            Id = userId,
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = currentPasswordHash,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        await context.SaveChangesAsync();

        var request = new ChangePasswordRequest
        {
            CurrentPassword = "CurrentPassword123!",
            NewPassword = "NewPassword456!",
            ConfirmPassword = "NewPassword456!"
        };

        var userService = new UserService(context);

        // Act
        var result = await userService.ChangePasswordAsync(userId, request);

        // Assert
        Assert.True(result);

        // Verify password was updated
        var updatedUser = await context.Users.FindAsync(userId);
        Assert.NotNull(updatedUser);
        Assert.NotEqual(currentPasswordHash, updatedUser.PasswordHash);
        Assert.True(BCrypt.Net.BCrypt.Verify(request.NewPassword, updatedUser.PasswordHash));
    }

    [Fact]
    public async Task ChangePasswordAsync_WithIncorrectCurrentPassword_ShouldThrowAuthenticationException()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var currentPasswordHash = BCrypt.Net.BCrypt.HashPassword("CurrentPassword123!");
        var user = new User
        {
            Id = userId,
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = currentPasswordHash,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        await context.SaveChangesAsync();

        var request = new ChangePasswordRequest
        {
            CurrentPassword = "WrongPassword!",
            NewPassword = "NewPassword456!",
            ConfirmPassword = "NewPassword456!"
        };

        var userService = new UserService(context);

        // Act & Assert
        await Assert.ThrowsAsync<AuthenticationException>(() => userService.ChangePasswordAsync(userId, request));
    }

    [Fact]
    public async Task ChangePasswordAsync_WithPasswordMismatch_ShouldThrowValidationException()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var currentPasswordHash = BCrypt.Net.BCrypt.HashPassword("CurrentPassword123!");
        var user = new User
        {
            Id = userId,
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = currentPasswordHash,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        await context.SaveChangesAsync();

        var request = new ChangePasswordRequest
        {
            CurrentPassword = "CurrentPassword123!",
            NewPassword = "NewPassword456!",
            ConfirmPassword = "DifferentPassword789!"
        };

        var userService = new UserService(context);

        // Act & Assert
        await Assert.ThrowsAsync<ValidationException>(() => userService.ChangePasswordAsync(userId, request));
    }
}
