using System.ComponentModel.DataAnnotations;
using NafaPlace.Inventory.Domain.Common;
using NafaPlace.Inventory.Domain.Enums;

namespace NafaPlace.Inventory.Domain.Models;

public class StockReservation : BaseEntity
{
    [Required]
    public int ProductId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string SessionId { get; set; }

    [Required]
    [Range(1, int.MaxValue)]
    public int Quantity { get; set; }

    [Required]
    public ReservationStatus Status { get; set; } = ReservationStatus.Active;

    [Required]
    public DateTime ReservedAt { get; set; } = DateTime.UtcNow;

    [Required]
    public DateTime ExpiresAt { get; set; }

    [MaxLength(50)]
    public string? OrderId { get; set; }

    [MaxLength(200)]
    public string? Reason { get; set; }

    public DateTime? ReleasedAt { get; set; }

    // Computed properties
    public bool IsExpired => DateTime.UtcNow > ExpiresAt;
    public bool IsActive => Status == ReservationStatus.Active && !IsExpired;
    public TimeSpan TimeRemaining => ExpiresAt > DateTime.UtcNow ? ExpiresAt - DateTime.UtcNow : TimeSpan.Zero;
}

public enum ReservationStatus
{
    Active = 1,      // Réservation active
    Confirmed = 2,   // Confirmée (commande passée)
    Released = 3,    // Libérée manuellement
    Expired = 4      // Expirée automatiquement
}

public class StockAlert : BaseEntity
{
    [Required]
    public int ProductId { get; set; }

    [Required]
    [MaxLength(100)]
    public required string ProductName { get; set; }

    [Required]
    public AlertType Type { get; set; }

    [Required]
    public AlertSeverity Severity { get; set; }

    [Required]
    [MaxLength(500)]
    public required string Message { get; set; }

    [Required]
    public int CurrentStock { get; set; }

    public int? ThresholdValue { get; set; }

    [Required]
    public bool IsActive { get; set; } = true;

    public bool IsAcknowledged { get; set; } = false;

    [MaxLength(50)]
    public string? AcknowledgedBy { get; set; }

    public DateTime? AcknowledgedAt { get; set; }

    [Required]
    public int SellerId { get; set; }

    [MaxLength(100)]
    public string? SellerName { get; set; }

    // Navigation properties
    public virtual List<StockAlertNotification> Notifications { get; set; } = new();
}

public enum AlertType
{
    LowStock = 1,        // Stock faible
    OutOfStock = 2,      // Rupture de stock
    OverStock = 3,       // Surstock
    StockMovement = 4,   // Mouvement de stock important
    ReservationExpiry = 5 // Expiration de réservation
}

public enum AlertSeverity
{
    Info = 1,     // Information
    Warning = 2,  // Avertissement
    Critical = 3, // Critique
    Emergency = 4 // Urgence
}

public class StockAlertNotification : BaseEntity
{
    [Required]
    public int AlertId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [Required]
    public NotificationChannel Channel { get; set; }

    [Required]
    [MaxLength(100)]
    public required string Recipient { get; set; } // Email, phone, etc.

    [Required]
    public NotificationStatus Status { get; set; } = NotificationStatus.Pending;

    public DateTime? SentAt { get; set; }

    [MaxLength(500)]
    public string? ErrorMessage { get; set; }

    public int RetryCount { get; set; } = 0;

    // Navigation properties
    public virtual StockAlert? Alert { get; set; }
}

public enum NotificationChannel
{
    Email = 1,
    SMS = 2,
    Push = 3,
    InApp = 4
}

public enum NotificationStatus
{
    Pending = 1,
    Sent = 2,
    Failed = 3,
    Delivered = 4
}

public class StockMovement : BaseEntity
{
    [Required]
    public int ProductId { get; set; }

    [Required]
    [MaxLength(100)]
    public required string ProductName { get; set; }

    [Required]
    public MovementType Type { get; set; }

    [Required]
    public int Quantity { get; set; }

    [Required]
    public int PreviousStock { get; set; }

    [Required]
    public int NewStock { get; set; }

    [Required]
    [MaxLength(200)]
    public required string Reason { get; set; }

    [MaxLength(50)]
    public string? Reference { get; set; } // Order ID, Adjustment ID, etc.

    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [MaxLength(100)]
    public string? UserName { get; set; }

    [Required]
    public int SellerId { get; set; }

    [MaxLength(500)]
    public string? Notes { get; set; }
}


