# Script PowerShell pour tester les nouvelles fonctionnalités NafaPlace
# Exécuter après le démarrage de Docker

Write-Host "🚀 Test des Nouvelles Fonctionnalités NafaPlace" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Configuration des URLs
$MainWebUrl = "http://localhost:8080"
$AdminPortalUrl = "http://localhost:8081"
$SellerPortalUrl = "http://localhost:8082"

# Fonction pour tester une URL
function Test-Url {
    param($Url, $Name)
    try {
        $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $Name - OK (Status: $($response.StatusCode))" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ $Name - Warning (Status: $($response.StatusCode))" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ $Name - Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour tester une API
function Test-Api {
    param($Url, $Name)
    try {
        $response = Invoke-RestMethod -Uri $Url -Method GET -TimeoutSec 10
        Write-Host "✅ API $Name - OK" -ForegroundColor Green
        return $response
    } catch {
        Write-Host "❌ API $Name - Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

Write-Host "`n🔍 1. Test de Connectivité des Portails" -ForegroundColor Cyan
Write-Host "----------------------------------------"

$mainWebOk = Test-Url $MainWebUrl "Main Web Portal"
$adminPortalOk = Test-Url $AdminPortalUrl "Admin Portal"
$sellerPortalOk = Test-Url $SellerPortalUrl "Seller Portal"

Write-Host "`n🎫 2. Test du Système de Coupons" -ForegroundColor Cyan
Write-Host "-----------------------------------"

# Test des APIs de coupons
$couponApiUrl = "$MainWebUrl/api/coupons"
Write-Host "Testing Coupon API: $couponApiUrl"

# Test de récupération des coupons
$coupons = Test-Api "$couponApiUrl" "Get Coupons"
if ($coupons) {
    Write-Host "📊 Nombre de coupons trouvés: $($coupons.Count)" -ForegroundColor Blue
}

# Test de validation d'un coupon spécifique
$testCouponCode = "WELCOME10"
Write-Host "🧪 Test du coupon: $testCouponCode"
$couponValidation = Test-Api "$couponApiUrl/validate/$testCouponCode" "Validate Coupon $testCouponCode"

Write-Host "`n📦 3. Test de la Gestion des Stocks" -ForegroundColor Cyan
Write-Host "------------------------------------"

# Test des APIs d'inventaire
$inventoryApiUrl = "$MainWebUrl/api/inventory"
Write-Host "Testing Inventory API: $inventoryApiUrl"

# Test de validation de stock pour un produit
$testProductId = 1
$testQuantity = 2
$stockValidation = Test-Api "$inventoryApiUrl/products/$testProductId/validate?quantity=$testQuantity" "Stock Validation"

if ($stockValidation) {
    Write-Host "📊 Stock disponible pour produit $testProductId : $($stockValidation.AvailableStock)" -ForegroundColor Blue
}

Write-Host "`n📧 4. Test du Système de Notifications" -ForegroundColor Cyan
Write-Host "---------------------------------------"

# Test des APIs de notifications
$notificationApiUrl = "$MainWebUrl/api/notifications"
Write-Host "Testing Notification API: $notificationApiUrl"

# Test de récupération des templates
$templates = Test-Api "$notificationApiUrl/templates" "Get Notification Templates"
if ($templates) {
    Write-Host "📊 Nombre de templates trouvés: $($templates.Count)" -ForegroundColor Blue
}

Write-Host "`n🚚 5. Test du Système de Livraison" -ForegroundColor Cyan
Write-Host "-----------------------------------"

# Test des APIs de livraison
$deliveryApiUrl = "$MainWebUrl/api/delivery"
Write-Host "Testing Delivery API: $deliveryApiUrl"

# Test de récupération des zones de livraison
$zones = Test-Api "$deliveryApiUrl/zones" "Get Delivery Zones"
if ($zones) {
    Write-Host "📊 Nombre de zones de livraison: $($zones.Count)" -ForegroundColor Blue
}

# Test de récupération des transporteurs
$carriers = Test-Api "$deliveryApiUrl/carriers" "Get Carriers"
if ($carriers) {
    Write-Host "📊 Nombre de transporteurs: $($carriers.Count)" -ForegroundColor Blue
}

# Test de calcul de frais de livraison
$testAddress = "Conakry, Kaloum"
$testOrderValue = 150000
$deliveryQuote = Test-Api "$deliveryApiUrl/quote?address=$testAddress&orderValue=$testOrderValue" "Delivery Quote"

if ($deliveryQuote) {
    Write-Host "💰 Frais de livraison pour $testAddress : $($deliveryQuote.DeliveryFee) GNF" -ForegroundColor Blue
}

Write-Host "`n🧪 6. Test des Fonctionnalités Frontend" -ForegroundColor Cyan
Write-Host "----------------------------------------"

# Test des pages spécifiques
$testPages = @(
    @{ Url = "$MainWebUrl/cart"; Name = "Page Panier (avec coupons)" },
    @{ Url = "$AdminPortalUrl/inventory"; Name = "Gestion des Stocks (Admin)" },
    @{ Url = "$SellerPortalUrl/products"; Name = "Produits Vendeur" },
    @{ Url = "$MainWebUrl/checkout"; Name = "Page Commande (avec livraison)" }
)

foreach ($page in $testPages) {
    Test-Url $page.Url $page.Name
    Start-Sleep -Seconds 1
}

Write-Host "`n📊 7. Résumé des Tests" -ForegroundColor Cyan
Write-Host "----------------------"

$totalTests = 0
$passedTests = 0

# Compter les résultats (simulation)
if ($mainWebOk) { $passedTests++ }; $totalTests++
if ($adminPortalOk) { $passedTests++ }; $totalTests++
if ($sellerPortalOk) { $passedTests++ }; $totalTests++

$successRate = [math]::Round(($passedTests / $totalTests) * 100, 2)

Write-Host "✅ Tests réussis: $passedTests/$totalTests ($successRate%)" -ForegroundColor Green

if ($successRate -ge 80) {
    Write-Host "`n🎉 SUCCÈS ! Les nouvelles fonctionnalités sont opérationnelles !" -ForegroundColor Green
} elseif ($successRate -ge 60) {
    Write-Host "`n⚠️ ATTENTION ! Certaines fonctionnalités nécessitent des corrections." -ForegroundColor Yellow
} else {
    Write-Host "`n❌ ÉCHEC ! Des problèmes majeurs ont été détectés." -ForegroundColor Red
}

Write-Host "`n🔗 URLs de Test:" -ForegroundColor Cyan
Write-Host "Main Web: $MainWebUrl"
Write-Host "Admin Portal: $AdminPortalUrl"
Write-Host "Seller Portal: $SellerPortalUrl"

Write-Host "`n📝 Prochaines étapes recommandées:" -ForegroundColor Cyan
Write-Host "1. Tester manuellement les fonctionnalités dans le navigateur"
Write-Host "2. Vérifier les logs Docker pour les erreurs"
Write-Host "3. Tester les scénarios utilisateur complets"
Write-Host "4. Valider les données en base de données"

Write-Host "`nTest terminé ! 🏁" -ForegroundColor Green
