using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using NafaPlace.Payment.API.Models;
using Stripe;
using Stripe.Checkout;
using System.Security.Claims;

namespace NafaPlace.Payment.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PaymentsController : ControllerBase
    {
        private readonly StripeSettings _stripeSettings;
        private readonly ILogger<PaymentsController> _logger;

        public PaymentsController(IOptions<StripeSettings> stripeSettings, ILogger<PaymentsController> logger)
        {
            _stripeSettings = stripeSettings.Value;
            _logger = logger;
            StripeConfiguration.ApiKey = _stripeSettings.SecretKey;
        }

        [HttpGet("config")]
        public ActionResult GetConfig()
        {
            return Ok(new { publishableKey = _stripeSettings.PublishableKey });
        }

        [HttpGet("payment-status/{paymentIntentId}")]
        public async Task<ActionResult<PaymentStatusResponse>> GetPaymentStatus(string paymentIntentId)
        {
            try
            {
                var service = new PaymentIntentService();
                var paymentIntent = await service.GetAsync(paymentIntentId);

                return Ok(new PaymentStatusResponse
                {
                    Status = paymentIntent.Status,
                    PaymentIntentId = paymentIntent.Id,
                    Amount = paymentIntent.Amount,
                    Currency = paymentIntent.Currency,
                    CreatedAt = paymentIntent.Created,
                    FailureReason = paymentIntent.LastPaymentError?.Message
                });
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Stripe error getting payment status");
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment status");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        [HttpPost("create-checkout-session")]
        // [Authorize] // Temporairement désactivé pour le développement
        public async Task<ActionResult> CreateCheckoutSession([FromBody] CreateCheckoutSessionRequest request)
        {
            try
            {
                // Temporaire : utiliser un userId par défaut pour le développement
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "dev-user-" + Guid.NewGuid().ToString()[..8];

                // Vérifier et ajuster le montant minimum pour Stripe
                const long STRIPE_MIN_AMOUNT_GNF = 10000; // 10,000 GNF minimum
                var totalAmount = request.Items.Sum(item => item.UnitAmount * item.Quantity);

                if (totalAmount < STRIPE_MIN_AMOUNT_GNF)
                {
                    _logger.LogWarning("Amount {Amount} GNF is below Stripe minimum. Adjusting to {MinAmount} GNF",
                        totalAmount, STRIPE_MIN_AMOUNT_GNF);

                    // Ajuster le montant du premier item pour atteindre le minimum
                    if (request.Items.Any())
                    {
                        var firstItem = request.Items.First();
                        var adjustment = STRIPE_MIN_AMOUNT_GNF - totalAmount;
                        firstItem.UnitAmount += adjustment;
                    }
                }

                var options = new SessionCreateOptions
                {
                    PaymentMethodTypes = new List<string> { "card" },
                    LineItems = request.Items.Select(item => new SessionLineItemOptions
                    {
                        PriceData = new SessionLineItemPriceDataOptions
                        {
                            Currency = request.Currency.ToLower(),
                            UnitAmount = item.UnitAmount,
                            ProductData = new SessionLineItemPriceDataProductDataOptions
                            {
                                Name = item.Name,
                                Images = !string.IsNullOrEmpty(item.ImageUrl) ? new List<string> { item.ImageUrl } : null
                            }
                        },
                        Quantity = item.Quantity
                    }).ToList(),
                    Mode = "payment",
                    SuccessUrl = request.SuccessUrl,
                    CancelUrl = request.CancelUrl,
                    ClientReferenceId = userId,
                    Metadata = new Dictionary<string, string>
                    {
                        { "user_id", userId },
                        { "order_id", request.OrderId ?? "" }
                    }
                };

                var service = new SessionService();
                var session = await service.CreateAsync(options);

                _logger.LogInformation("Created Stripe checkout session {SessionId} for user {UserId}", session.Id, userId);

                return Ok(new { sessionId = session.Id, sessionUrl = session.Url });
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Stripe error creating checkout session");
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating checkout session");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        [HttpPost("webhook")]
        public async Task<IActionResult> HandleWebhook()
        {
            var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();

            try
            {
                var stripeEvent = EventUtility.ConstructEvent(
                    json,
                    Request.Headers["Stripe-Signature"],
                    _stripeSettings.WebhookSecret
                );

                _logger.LogInformation("Received Stripe webhook: {EventType}", stripeEvent.Type);

                switch (stripeEvent.Type)
                {
                    case "checkout.session.completed":
                        await HandleCheckoutSessionCompleted(stripeEvent);
                        break;
                    case "payment_intent.succeeded":
                        await HandlePaymentIntentSucceeded(stripeEvent);
                        break;
                    case "payment_intent.payment_failed":
                        await HandlePaymentIntentFailed(stripeEvent);
                        break;
                    default:
                        _logger.LogInformation("Unhandled event type: {EventType}", stripeEvent.Type);
                        break;
                }

                return Ok();
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Stripe webhook error");
                return BadRequest();
            }
        }

        [HttpGet("session/{sessionId}")]
        [Authorize]
        public async Task<ActionResult> GetSession(string sessionId)
        {
            try
            {
                var service = new SessionService();
                var session = await service.GetAsync(sessionId);

                return Ok(new
                {
                    id = session.Id,
                    status = session.Status,
                    paymentStatus = session.PaymentStatus,
                    amountTotal = session.AmountTotal,
                    currency = session.Currency,
                    customerEmail = session.CustomerEmail
                });
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Error retrieving Stripe session {SessionId}", sessionId);
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("refund")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> CreateRefund([FromBody] CreateRefundRequest request)
        {
            try
            {
                var options = new RefundCreateOptions
                {
                    PaymentIntent = request.PaymentIntentId,
                    Amount = request.Amount,
                    Reason = request.Reason
                };

                var service = new RefundService();
                var refund = await service.CreateAsync(options);

                _logger.LogInformation("Created refund {RefundId} for payment intent {PaymentIntentId}",
                    refund.Id, request.PaymentIntentId);

                return Ok(new
                {
                    id = refund.Id,
                    status = refund.Status,
                    amount = refund.Amount,
                    currency = refund.Currency
                });
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Error creating refund");
                return BadRequest(new { error = ex.Message });
            }
        }

        private async Task HandleCheckoutSessionCompleted(Event stripeEvent)
        {
            var session = stripeEvent.Data.Object as Session;
            if (session != null)
            {
                _logger.LogInformation("Checkout session completed: {SessionId}", session.Id);
                // TODO: Update order status, send confirmation email, etc.
            }
        }

        private async Task HandlePaymentIntentSucceeded(Event stripeEvent)
        {
            var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
            if (paymentIntent != null)
            {
                _logger.LogInformation("Payment succeeded: {PaymentIntentId}", paymentIntent.Id);
                // TODO: Update order status, fulfill order, etc.
            }
        }

        private async Task HandlePaymentIntentFailed(Event stripeEvent)
        {
            var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
            if (paymentIntent != null)
            {
                _logger.LogWarning("Payment failed: {PaymentIntentId}", paymentIntent.Id);
                // TODO: Update order status, notify customer, etc.
            }
        }
    }
}