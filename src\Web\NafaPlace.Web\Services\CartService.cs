using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using NafaPlace.Web.Models.Cart;
using System.Text.Json;

namespace NafaPlace.Web.Services
{
    public class CartService : ICartService
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly CartNotificationService _notificationService;

        public CartService(HttpClient httpClient, CartNotificationService notificationService)
        {
            _httpClient = httpClient;
            _notificationService = notificationService;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<CartDto> GetCartAsync(string userId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/cart/{userId}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var cartData = JsonSerializer.Deserialize<CartDto>(content, _jsonOptions);
                    return cartData ?? new CartDto { UserId = userId };
                }
                return new CartDto { UserId = userId };
            }
            catch
            {
                return new CartDto { UserId = userId };
            }
        }

        public async Task<CartDto> AddItemToCartAsync(string userId, CartItemCreateDto item)
        {
            Console.WriteLine($"🛒 DEBUG CartService: AddItemToCartAsync - UserId: {userId}, ProductId: {item.ProductId}");

            var request = new
            {
                ProductId = item.ProductId,
                Quantity = item.Quantity
            };

            Console.WriteLine($"📡 DEBUG CartService: Appel POST /api/cart/{userId}/items");
            var response = await _httpClient.PostAsJsonAsync($"/api/cart/{userId}/items", request);

            Console.WriteLine($"📊 DEBUG CartService: Response Status: {response.StatusCode}");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"📄 DEBUG CartService: Response Content: {content}");

            var result = JsonSerializer.Deserialize<CartDto>(content, _jsonOptions) ?? new CartDto { UserId = userId };
            Console.WriteLine($"🔔 DEBUG CartService: Déclenchement de NotifyCartUpdated...");

            _notificationService.NotifyCartUpdated();
            Console.WriteLine($"✅ DEBUG CartService: AddItemToCartAsync terminé - ItemCount: {result.ItemCount}");

            return result;
        }

        public async Task<CartDto> UpdateItemInCartAsync(string userId, CartItemDto item)
        {
            var request = new
            {
                ProductId = item.ProductId,
                Quantity = item.Quantity
            };

            var response = await _httpClient.PutAsJsonAsync($"/api/cart/{userId}/items", request);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<CartDto>(content, _jsonOptions) ?? new CartDto { UserId = userId };

            _notificationService.NotifyCartUpdated();
            return result;
        }

        public async Task<CartDto> RemoveItemFromCartAsync(string userId, int productId)
        {
            var response = await _httpClient.DeleteAsync($"/api/cart/{userId}/items/{productId}");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<CartDto>(content, _jsonOptions) ?? new CartDto { UserId = userId };

            _notificationService.NotifyCartUpdated();
            return result;
        }

        public async Task<bool> ClearCartAsync(string userId)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/cart/{userId}");
                if (response.IsSuccessStatusCode)
                {
                    _notificationService.NotifyCartUpdated();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<CartSummaryDto> GetCartSummaryAsync(string userId)
        {
            Console.WriteLine($"📊 DEBUG CartService: GetCartSummaryAsync - UserId: {userId}");

            try
            {
                var response = await _httpClient.GetAsync($"/api/cart/{userId}/summary");
                Console.WriteLine($"📡 DEBUG CartService: Summary Response Status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"📄 DEBUG CartService: Summary Content: {content}");

                    var result = JsonSerializer.Deserialize<CartSummaryDto>(content, _jsonOptions) ?? new CartSummaryDto();
                    Console.WriteLine($"✅ DEBUG CartService: Summary ItemCount: {result.ItemCount}");

                    return result;
                }
                Console.WriteLine($"❌ DEBUG CartService: Summary failed - Status: {response.StatusCode}");
                return new CartSummaryDto();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ DEBUG CartService: Summary exception: {ex.Message}");
                return new CartSummaryDto();
            }
        }

        public async Task<CartDto> ApplyCouponAsync(string userId, string couponCode)
        {
            try
            {
                var request = new { CouponCode = couponCode };
                var response = await _httpClient.PostAsJsonAsync($"/api/cart/{userId}/coupon", request);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<CartDto>(content, _jsonOptions) ?? new CartDto { UserId = userId };

                _notificationService.NotifyCartUpdated();
                return result;
            }
            catch
            {
                return new CartDto { UserId = userId };
            }
        }

        public async Task<CartDto> RemoveCouponAsync(string userId)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/cart/{userId}/coupon");
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<CartDto>(content, _jsonOptions) ?? new CartDto { UserId = userId };

                _notificationService.NotifyCartUpdated();
                return result;
            }
            catch
            {
                return new CartDto { UserId = userId };
            }
        }
    }
}
