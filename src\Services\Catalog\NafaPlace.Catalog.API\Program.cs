using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using NafaPlace.Catalog.Application;
using NafaPlace.Catalog.Infrastructure;
using NafaPlace.Catalog.Infrastructure.Persistence;
using NafaPlace.Catalog.Infrastructure.Services;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);

// Configuration des services
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);

builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
        options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    });

// Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "NafaPlace Catalog API",
        Version = "v1",
        Description = "API de gestion du catalogue pour la plateforme e-commerce NafaPlace",
        Contact = new OpenApiContact
        {
            Name = "Support NafaPlace",
            Email = "<EMAIL>"
        }
    });

    // Configuration de l'authentification JWT
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Inclure les commentaires XML pour la documentation (si le fichier existe)
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Configuration CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowedOrigins", policy =>
    {
        policy.WithOrigins(
                "http://localhost:8080",   // Client Portal
                "http://localhost:8081",   // Admin Portal
                "http://localhost:8082",   // Seller Portal
                "https://nafaplace-web-test.fly.dev",     // Production Client Portal
                "https://nafaplace-admin-test.fly.dev",   // Production Admin Portal
                "https://nafaplace-seller-test.fly.dev"   // Production Seller Portal
            )
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials();
    });
});

// Configuration de l'authentification
builder.Services.AddAuthentication("Bearer")
    .AddJwtBearer(options =>
    {
        options.Authority = builder.Configuration["IdentityUrl"];
        options.RequireHttpsMetadata = false;
        options.TokenValidationParameters.ValidateAudience = false;
    });

var app = builder.Build();

// Middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "NafaPlace Catalog API V1");
        c.RoutePrefix = string.Empty;
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowedOrigins");
app.UseAuthentication();
app.UseAuthorization();

// Log des contrôleurs découverts pour diagnostic
var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("🔍 Mapping controllers...");
app.MapControllers();
logger.LogInformation("✅ Controllers mapped successfully");

// Initialize and seed the database (non-blocking)
_ = Task.Run(async () =>
{
    using var scope = app.Services.CreateScope();
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<CatalogDbContext>();
        await context.Database.MigrateAsync();
        logger.LogInformation("✅ Database migration completed successfully");
    }
    catch (Exception ex)
    {
        var dbLogger = services.GetRequiredService<ILogger<Program>>();
        dbLogger.LogError(ex, "❌ An error occurred while migrating the database.");
    }
});

app.Run();
