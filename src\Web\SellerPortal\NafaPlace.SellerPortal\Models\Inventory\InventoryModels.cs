namespace NafaPlace.SellerPortal.Models.Inventory;

public class InventoryDashboardDto
{
    public int TotalProducts { get; set; }
    public int LowStockCount { get; set; }
    public int OutOfStockProducts { get; set; }
    public int ActiveReservations { get; set; }
    public int PendingAlerts { get; set; }
    public decimal TotalInventoryValue { get; set; }
    public string Currency { get; set; } = "GNF";
    public List<StockAlertDto> RecentAlerts { get; set; } = new();
    public List<StockMovementDto> RecentMovements { get; set; } = new();
    public List<TopProductDto> TopSellingProducts { get; set; } = new();
    public List<TopProductDto> LowStockProducts { get; set; } = new();
}

public class StockAlertDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public AlertType Type { get; set; }
    public AlertSeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public int CurrentStock { get; set; }
    public int? ThresholdValue { get; set; }
    public bool IsActive { get; set; }
    public bool IsAcknowledged { get; set; }
    public string? AcknowledgedBy { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public int SellerId { get; set; }
    public string? SellerName { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class StockMovementDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public MovementType Type { get; set; }
    public int Quantity { get; set; }
    public int PreviousStock { get; set; }
    public int NewStock { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Reference { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string? UserName { get; set; }
    public int SellerId { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class TopProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int CurrentStock { get; set; }
    public int SoldQuantity { get; set; }
    public decimal Revenue { get; set; }
    public string Currency { get; set; } = "GNF";
}

public class UpdateStockRequest
{
    public int NewQuantity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class StockAdjustmentRequest
{
    public int ProductId { get; set; }
    public int NewQuantity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public enum AlertType
{
    LowStock = 1,        // Stock faible
    OutOfStock = 2,      // Rupture de stock
    OverStock = 3,       // Surstock
    StockMovement = 4,   // Mouvement de stock important
    ReservationExpiry = 5 // Expiration de réservation
}

public enum AlertSeverity
{
    Info = 1,     // Information
    Warning = 2,  // Avertissement
    Critical = 3, // Critique
    Emergency = 4 // Urgence
}

public enum MovementType
{
    Purchase = 1,     // Achat/Réapprovisionnement
    Sale = 2,         // Vente
    Return = 3,       // Retour
    Adjustment = 4,   // Ajustement
    Transfer = 5,     // Transfert
    Damage = 6,       // Dommage/Perte
    Reservation = 7,  // Réservation
    Release = 8       // Libération de réservation
}

public class StockValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public int AvailableStock { get; set; }
    public int ReservedStock { get; set; }
    public int RequestedQuantity { get; set; }
    public bool RequiresReservation { get; set; }
}
