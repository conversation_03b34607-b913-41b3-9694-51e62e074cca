using Microsoft.Extensions.Configuration;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Services;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Xunit;

namespace NafaPlace.Identity.UnitTests.Services;

public class TokenValidationTests
{
    private readonly IConfiguration _configuration;
    private readonly JwtService _jwtService;
    private readonly User _testUser;

    public TokenValidationTests()
    {
        // Configuration pour les tests
        var inMemorySettings = new Dictionary<string, string>
        {
            {"JwtSettings:Secret", "NafaPlaceSecretKey2025ForProductionEnvironment"},
            {"JwtSettings:Issuer", "NafaPlace"},
            {"JwtSettings:Audience", "NafaPlaceApi"},
            {"JwtSettings:TokenLifetimeMinutes", "60"}
        };

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(inMemorySettings)
            .Build();

        _jwtService = new JwtService(_configuration);

        _testUser = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Username = "testuser"
        };
    }

    [Fact]
    public void ValidateToken_WithExpiredToken_ShouldReturnFalse()
    {
        // Arrange - Créer un token expiré manuellement
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = System.Text.Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"]);
        var tokenDescriptor = new Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, _testUser.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Email, _testUser.Email),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            }),
            Expires = DateTime.UtcNow.AddMinutes(-10), // Token expiré
            Issuer = _configuration["JwtSettings:Issuer"],
            Audience = _configuration["JwtSettings:Audience"],
            SigningCredentials = new Microsoft.IdentityModel.Tokens.SigningCredentials(
                new Microsoft.IdentityModel.Tokens.SymmetricSecurityKey(key),
                Microsoft.IdentityModel.Tokens.SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.WriteToken(tokenHandler.CreateToken(tokenDescriptor));

        // Act
        var isValid = _jwtService.ValidateToken(token);

        // Assert
        Assert.False(isValid);
    }

    [Fact]
    public void ValidateToken_WithModifiedSignature_ShouldReturnFalse()
    {
        // Arrange
        var token = _jwtService.GenerateAccessToken(_testUser.Id, _testUser.Username, new List<string> { "User" });
        // Modifier le token pour simuler une altération
        var modifiedToken = token + "a";

        // Act
        var isValid = _jwtService.ValidateToken(modifiedToken);

        // Assert
        Assert.False(isValid);
    }

    [Fact]
    public void ValidateToken_WithInvalidIssuer_ShouldReturnFalse()
    {
        // Arrange - Créer un token avec un émetteur invalide
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = System.Text.Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"]);
        var tokenDescriptor = new Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, _testUser.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Email, _testUser.Email),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            }),
            Expires = DateTime.UtcNow.AddHours(1),
            Issuer = "InvalidIssuer",
            Audience = _configuration["JwtSettings:Audience"],
            SigningCredentials = new Microsoft.IdentityModel.Tokens.SigningCredentials(
                new Microsoft.IdentityModel.Tokens.SymmetricSecurityKey(key),
                Microsoft.IdentityModel.Tokens.SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.WriteToken(tokenHandler.CreateToken(tokenDescriptor));

        // Act
        var isValid = _jwtService.ValidateToken(token);

        // Assert
        Assert.False(isValid);
    }

    [Fact]
    public void GenerateAccessToken_ShouldCreateValidToken()
    {
        // Act
        var token = _jwtService.GenerateAccessToken(_testUser.Id, _testUser.Username, new List<string> { "User" });

        // Assert
        Assert.NotNull(token);
        Assert.NotEmpty(token);

        var principal = _jwtService.GetPrincipalFromExpiredToken(token);
        Assert.NotNull(principal);

        var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier);
        Assert.NotNull(userIdClaim);
        Assert.Equal(_testUser.Id.ToString(), userIdClaim.Value);

        var usernameClaim = principal.FindFirst(ClaimTypes.Name);
        Assert.NotNull(usernameClaim);
        Assert.Equal(_testUser.Username, usernameClaim.Value);

        var roleClaim = principal.FindFirst(ClaimTypes.Role);
        Assert.NotNull(roleClaim);
        Assert.Equal("User", roleClaim.Value);
    }

    [Fact]
    public void GetClaimsFromToken_WithValidToken_ShouldReturnClaims()
    {
        // Arrange
        var roles = new[] { "User", "Admin" };
        var token = _jwtService.GenerateAccessToken(_testUser.Id, _testUser.Username, roles);

        // Act
        var claims = _jwtService.GetClaimsFromToken(token);

        // Assert
        Assert.NotNull(claims);
        Assert.Contains(claims, c => c.Type == JwtRegisteredClaimNames.Sub && c.Value == _testUser.Id.ToString());
        Assert.Contains(claims, c => c.Type == JwtRegisteredClaimNames.Email && c.Value == _testUser.Email);
        Assert.Contains(claims, c => c.Type == ClaimTypes.Role && c.Value == "User");
        Assert.Contains(claims, c => c.Type == ClaimTypes.Role && c.Value == "Admin");
    }

    [Fact]
    public void GetClaimsFromToken_WithInvalidToken_ShouldReturnNull()
    {
        // Act
        var claims = _jwtService.GetClaimsFromToken("invalid-token");

        // Assert
        Assert.Null(claims);
    }
}
