# Feuille de Route NafaPlace

Ce document décrit la feuille de route pour le développement de la plateforme e-commerce NafaPlace.

## Plan d'Architecture

Une plateforme e-commerce moderne repose sur une architecture de microservices. Chaque service est responsable d'un domaine métier spécifique.

Voici les modules clés que nous allons construire ou améliorer :

1.  **Gestion des Produits (Catalogue)**
2.  **Gestion des Utilisateurs (Identité)**
3.  **Panier d'Achat**
4.  **Gestion des Commandes**
5.  **Intégration de Paiement**
6.  **Recherche & Navigation**
7.  **Avis & Évaluations**
8.  **Notifications**

### Visualisation de l'Architecture

```mermaid
graph TD
    subgraph "Portails Utilisateurs"
        A[Client Web - NafaPlace.Web]
        B[Portail Vendeur]
        C[Portail Administrateur]
    end

    subgraph "Services Backend"
        D[API Gateway / BFF]
        E[Service Identité]
        F[Service Catalogue]
        G[Service Panier]
        H[Service Commandes]
        I[Service Paiements]
        J[Service Notifications]
        K[Service Avis]
    end

    subgraph "Bases de Données"
        DB1[DB Identité]
        DB2[DB Catalogue]
        DB3[DB Commandes]
        DB4[DB Avis]
    end

    subgraph "Services Externes"
        P[Passerelle de Paiement]
        S[Service d'envoi d'e-mails]
    end

    A & B & C --> D

    D --> E
    D --> F
    D --> G
    D --> H
    D --> I
    D --> J
    D --> K

    E --- DB1
    F --- DB2
    H --- DB3
    K --- DB4
    G --- Redis[(Cache Redis)]

    I --> P
    J --> S
```

## Prochaines Étapes

1.  **Améliorer la Gestion des Produits** (Terminé)
2.  **Afficher les produits sur la page d'accueil et le catalogue** (Terminé)
3.  **Intégrer le paiement avec Stripe** (En cours)
