@page "/account/orders/{OrderId:int}"
@using NafaPlace.Web.Models.Order
@using NafaPlace.Web.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using NafaPlace.Reviews.DTOs
@using NafaPlace.Web.Components.Reviews
@inject IOrderService OrderService
@inject IReviewService ReviewService
@inject NavigationManager NavigationManager

<PageTitle>Détails de la Commande - NafaPlace</PageTitle>

<div class="container mt-4">
    @if (_loading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2">Chargement des détails de la commande...</p>
        </div>
    }
    else if (_order == null)
    {
        <div class="alert alert-danger">
            <h4>Commande introuvable</h4>
            <p>La commande demandée n'existe pas ou vous n'avez pas l'autorisation de la consulter.</p>
            <a href="/account/orders" class="btn btn-primary">Retour à mes commandes</a>
        </div>
    }
    else
    {
        <!-- En-tête -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>Commande #@_order.Id.ToString().PadLeft(8, '0')</h2>
                <p class="text-muted mb-0">Passée le @_order.CreatedAt.ToString("dd/MM/yyyy à HH:mm")</p>
            </div>
            <div>
                <a href="/account/orders" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Retour
                </a>
                @if (_order.PaymentStatus != "Completed")
                {
                    <a href="/payment/simple/@_order.Id" class="btn btn-warning">
                        <i class="fas fa-credit-card me-2"></i>Finaliser le paiement
                    </a>
                }
            </div>
        </div>

        <div class="row">
            <!-- Informations principales -->
            <div class="col-md-8">
                <!-- Statut de la commande -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>Statut de la commande</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Statut:</strong>
                                <span class="badge @GetStatusBadgeClass(_order.Status) ms-2">
                                    @GetStatusText(_order.Status)
                                </span>
                            </div>
                            <div class="col-md-6">
                                <strong>Paiement:</strong>
                                <span class="badge @GetPaymentStatusBadgeClass(_order.PaymentStatus) ms-2">
                                    @GetPaymentStatusText(_order.PaymentStatus)
                                </span>
                            </div>
                        </div>
                        @if (!string.IsNullOrEmpty(_order.TransactionId))
                        {
                            <div class="mt-2">
                                <small class="text-muted">ID Transaction: @_order.TransactionId</small>
                            </div>
                        }
                    </div>
                </div>

                <!-- Adresse de livraison -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-map-marker-alt me-2"></i>Adresse de livraison</h5>
                    </div>
                    <div class="card-body">
                        @if (_order.ShippingAddress != null)
                        {
                            <address>
                                <strong>@_order.ShippingAddress.FullName</strong><br>
                                @_order.ShippingAddress.Address<br>
                                @_order.ShippingAddress.City, @_order.ShippingAddress.PostalCode<br>
                                @_order.ShippingAddress.Country<br>
                                <abbr title="Téléphone">Tél:</abbr> @_order.ShippingAddress.PhoneNumber
                            </address>
                        }
                        else
                        {
                            <p class="text-muted">Aucune adresse de livraison spécifiée</p>
                        }
                    </div>
                </div>

                <!-- Articles commandés -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-shopping-cart me-2"></i>Articles commandés</h5>
                    </div>
                    <div class="card-body">
                        @if (_order.Items != null && _order.Items.Any())
                        {
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Article</th>
                                            <th>Prix unitaire</th>
                                            <th>Quantité</th>
                                            <th class="text-end">Total</th>
                                            @if (CanLeaveReviews())
                                            {
                                                <th class="text-center">Avis</th>
                                            }
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in _order.Items)
                                        {
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        @if (!string.IsNullOrEmpty(item.ProductImageUrl))
                                                        {
                                                            <img src="@item.ProductImageUrl" alt="@item.ProductName" 
                                                                 class="me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                                        }
                                                        <div>
                                                            <strong>@item.ProductName</strong>
                                                            @if (!string.IsNullOrEmpty(item.ProductDescription))
                                                            {
                                                                <br><small class="text-muted">@item.ProductDescription</small>
                                                            }
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>@item.UnitPrice.ToString("N0") GNF</td>
                                                <td>@item.Quantity</td>
                                                <td class="text-end">@((item.UnitPrice * item.Quantity).ToString("N0")) GNF</td>
                                                @if (CanLeaveReviews())
                                                {
                                                    <td class="text-center">
                                                        @if (_existingReviews.ContainsKey(item.ProductId))
                                                        {
                                                            <div class="review-status">
                                                                <StarRating Rating="_existingReviews[item.ProductId].Rating" CssClass="small" />
                                                                <br>
                                                                <small class="text-success">
                                                                    <i class="fas fa-check-circle me-1"></i>Avis donné
                                                                </small>
                                                                <br>
                                                                <button class="btn btn-sm btn-outline-primary mt-1"
                                                                        @onclick="() => EditReview(item.ProductId)">
                                                                    Modifier
                                                                </button>
                                                            </div>
                                                        }
                                                        else
                                                        {
                                                            <button class="btn btn-sm btn-primary"
                                                                    @onclick="() => ShowReviewForm(item.ProductId, item.ProductName)">
                                                                <i class="fas fa-star me-1"></i>Donner un avis
                                                            </button>
                                                        }
                                                    </td>
                                                }
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted">Aucun article dans cette commande</p>
                        }
                    </div>
                </div>

                <!-- Review Form Modal -->
                @if (_showReviewForm && _selectedProductId > 0)
                {
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>
                                <i class="fas fa-star me-2"></i>
                                @(_editingReview ? "Modifier votre avis" : "Donner votre avis") - @_selectedProductName
                            </h5>
                        </div>
                        <div class="card-body">
                            <ReviewForm ProductId="_selectedProductId"
                                       ExistingReview="_editingReview ? _existingReviews[_selectedProductId] : null"
                                       IsEdit="_editingReview"
                                       ShowVerifiedPurchase="false"
                                       OnSubmit="HandleCreateReview"
                                       OnUpdate="HandleUpdateReview"
                                       OnCancel="HideReviewForm" />
                        </div>
                    </div>
                }
            </div>

            <!-- Résumé -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-calculator me-2"></i>Résumé</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Sous-total:</span>
                            <span>@GetSubTotal().ToString("N0") GNF</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Livraison:</span>
                            <span>Gratuite</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Total:</span>
                            <span class="text-primary">@_order.TotalAmount.ToString("N0") GNF</span>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                Mode de paiement: @GetPaymentMethodText(_order.PaymentMethod)
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6>Actions</h6>
                    </div>
                    <div class="card-body">
                        @if (_order.PaymentStatus != "Completed")
                        {
                            <a href="/payment/simple/@_order.Id" class="btn btn-warning w-100 mb-2">
                                <i class="fas fa-credit-card me-2"></i>Finaliser le paiement
                            </a>
                        }
                        
                        @if (_order.Status == "Shipped")
                        {
                            <button class="btn btn-info w-100 mb-2">
                                <i class="fas fa-truck me-2"></i>Suivre la livraison
                            </button>
                        }
                        
                        <a href="/" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-shopping-cart me-2"></i>Continuer mes achats
                        </a>

                        <a href="/account/orders" class="btn btn-outline-info w-100">
                            <i class="fas fa-list me-2"></i>Voir mes commandes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int OrderId { get; set; }
    
    private OrderDto? _order;
    private bool _loading = true;
    private string _userId = string.Empty;

    // Reviews related properties
    private Dictionary<int, ReviewDto> _existingReviews = new();
    private bool _showReviewForm = false;
    private int _selectedProductId = 0;
    private string _selectedProductName = string.Empty;
    private bool _editingReview = false;

    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var authState = await AuthenticationStateTask;
            var user = authState.User;
            
            if (user.Identity?.IsAuthenticated == true)
            {
                _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
                _order = await OrderService.GetOrderByIdAsync(OrderId);
                
                // Vérifier que la commande appartient à l'utilisateur connecté
                if (_order != null && _order.UserId != _userId)
                {
                    _order = null; // Pas autorisé à voir cette commande
                }
                else if (_order != null)
                {
                    // Charger les reviews existantes pour cette commande
                    await LoadExistingReviews();
                }
            }
            else
            {
                NavigationManager.NavigateTo("/auth/login");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de la commande: {ex.Message}");
        }
        finally
        {
            _loading = false;
        }
    }

    private decimal GetSubTotal()
    {
        return _order?.Items?.Sum(i => i.UnitPrice * i.Quantity) ?? 0;
    }

    private string GetStatusText(string? status)
    {
        return status switch
        {
            "Pending" => "En attente",
            "Confirmed" => "Confirmée",
            "Processing" => "En préparation",
            "Shipped" => "Expédiée",
            "Delivered" => "Livrée",
            "Cancelled" => "Annulée",
            _ => "Statut inconnu"
        };
    }

    private string GetStatusBadgeClass(string? status)
    {
        return status switch
        {
            "Pending" => "bg-warning",
            "Confirmed" => "bg-info",
            "Processing" => "bg-primary",
            "Shipped" => "bg-secondary",
            "Delivered" => "bg-success",
            "Cancelled" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentStatusText(string? status)
    {
        return status switch
        {
            "Pending" => "En attente",
            "Completed" => "Payé",
            "Failed" => "Échoué",
            "Cancelled" => "Annulé",
            _ => "Inconnu"
        };
    }

    private string GetPaymentStatusBadgeClass(string? status)
    {
        return status switch
        {
            "Pending" => "bg-warning",
            "Completed" => "bg-success",
            "Failed" => "bg-danger",
            "Cancelled" => "bg-secondary",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentMethodText(string? method)
    {
        return method switch
        {
            "CashOnDelivery" => "Paiement à la livraison",
            "Stripe" => "Carte bancaire",
            "OrangeMoney" => "Orange Money",
            _ => "Non spécifié"
        };
    }

    private bool CanLeaveReviews()
    {
        return _order?.Status == "Delivered" && _order?.PaymentStatus == "Completed";
    }

    private async Task LoadExistingReviews()
    {
        if (_order?.Items == null) return;

        try
        {
            foreach (var item in _order.Items)
            {
                var userReviews = await ReviewService.GetUserReviewsAsync(_userId, 1, 100);
                var existingReview = userReviews.Reviews.FirstOrDefault(r => r.ProductId == item.ProductId);
                if (existingReview != null)
                {
                    _existingReviews[item.ProductId] = existingReview;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des reviews existantes: {ex.Message}");
        }
    }

    private void ShowReviewForm(int productId, string productName)
    {
        _selectedProductId = productId;
        _selectedProductName = productName;
        _editingReview = false;
        _showReviewForm = true;
    }

    private void EditReview(int productId)
    {
        if (_existingReviews.ContainsKey(productId))
        {
            _selectedProductId = productId;
            _selectedProductName = _order?.Items?.FirstOrDefault(i => i.ProductId == productId)?.ProductName ?? "";
            _editingReview = true;
            _showReviewForm = true;
        }
    }

    private void HideReviewForm()
    {
        _showReviewForm = false;
        _selectedProductId = 0;
        _selectedProductName = string.Empty;
        _editingReview = false;
    }

    private async Task HandleCreateReview(CreateReviewRequest request)
    {
        try
        {
            // Marquer comme achat vérifié puisque c'est depuis une commande
            request.IsVerifiedPurchase = true;

            var createdReview = await ReviewService.CreateReviewAsync(request);
            if (createdReview != null)
            {
                _existingReviews[request.ProductId] = createdReview;
            }

            HideReviewForm();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création de la review: {ex.Message}");
        }
    }

    private async Task HandleUpdateReview(UpdateReviewRequest request)
    {
        try
        {
            var reviewId = _existingReviews[_selectedProductId].Id;
            var updatedReview = await ReviewService.UpdateReviewAsync(reviewId, request);
            if (updatedReview != null)
            {
                _existingReviews[_selectedProductId] = updatedReview;
            }

            HideReviewForm();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour de la review: {ex.Message}");
        }
    }
}
