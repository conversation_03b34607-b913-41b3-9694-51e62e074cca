# 🛒 Guide de Test Complet du Panier NafaPlace

## 🎯 Objectif
Tester toutes les fonctionnalités du panier après les corrections apportées :
- ✅ Indicateur de panier qui s'incrémente
- ✅ Ajout de produits avec notifications
- ✅ Affichage des images dans le panier
- ✅ Gestion des quantités
- ✅ Suppression de produits

## 🚀 Étapes de Test

### 1️⃣ **Préparation**
```bash
# Vérifier que tous les services sont actifs
docker-compose ps

# L'application doit être accessible sur :
# http://localhost:8080
```

### 2️⃣ **Test de Connexion**
1. Aller sur http://localhost:8080
2. Cliquer sur "Se connecter" en haut à droite
3. Utiliser les identifiants :
   - **Email** : `<EMAIL>`
   - **Mot de passe** : `Admin123!`
4. ✅ **Vérifier** : Vous devez être connecté et voir votre nom

### 3️⃣ **Test d'Ajout au Panier**
1. Sur la page d'accueil, localiser un produit
2. Cliquer sur "Ajouter au panier" 
3. ✅ **Vérifier** : 
   - Une notification toast verte apparaît : "✅ [Nom du produit] ajouté au panier !"
   - L'indicateur panier (en haut à droite) passe de "0" à "1"

4. Ajouter 2-3 autres produits
5. ✅ **Vérifier** : L'indicateur s'incrémente à chaque ajout

### 4️⃣ **Test de la Page Panier**
1. Cliquer sur l'icône panier (en haut à droite)
2. ✅ **Vérifier** : 
   - Vous arrivez sur la page panier
   - Tous les produits ajoutés sont visibles
   - Les images des produits s'affichent correctement
   - Les prix sont en GNF (Francs Guinéens)

### 5️⃣ **Test de Gestion des Quantités**
1. Dans le panier, localiser les boutons "+" et "-" 
2. Cliquer sur "+" pour augmenter la quantité
3. ✅ **Vérifier** : 
   - La quantité augmente
   - Le prix total se met à jour
   - L'indicateur panier se met à jour

4. Cliquer sur "-" pour diminuer
5. ✅ **Vérifier** : Même comportement en sens inverse

### 6️⃣ **Test de Suppression**
1. Cliquer sur l'icône poubelle 🗑️ d'un produit
2. ✅ **Vérifier** : 
   - Le produit disparaît du panier
   - Les totaux se recalculent
   - L'indicateur panier se met à jour

### 7️⃣ **Test de Vidage Complet**
1. Cliquer sur "Vider le panier"
2. Confirmer dans la popup
3. ✅ **Vérifier** : 
   - Le panier devient vide
   - Message "Votre panier est vide" s'affiche
   - L'indicateur panier revient à "0"

## 🐛 Résolution de Problèmes

### **L'indicateur reste à 0**
- Vérifiez que vous êtes bien connecté
- Actualisez la page (Ctrl+F5)
- Vérifiez la console du navigateur (F12)

### **Pas de notification toast**
- Vérifiez que JavaScript est activé
- Ouvrez la console (F12) pour voir les erreurs

### **Images ne s'affichent pas**
- Normal si les produits n'ont pas d'images configurées
- Une icône placeholder devrait s'afficher

### **Erreur lors de l'ajout**
- Vérifiez que l'API Cart fonctionne : http://localhost:5003
- Vérifiez les logs Docker : `docker-compose logs cart-api`

## 🎉 Résultat Attendu

À la fin des tests, vous devriez avoir :
- ✅ Un panier fonctionnel avec indicateur dynamique
- ✅ Des notifications visuelles pour chaque action
- ✅ Une gestion complète des quantités
- ✅ Un affichage correct des images et prix
- ✅ Une expérience utilisateur fluide

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifiez les logs : `docker-compose logs web`
2. Redémarrez les services : `docker-compose restart`
3. Vérifiez la console du navigateur (F12)
