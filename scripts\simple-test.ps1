# Simple test script for NafaPlace services
Write-Host "NafaPlace Services Test" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green

# Test URLs
$urls = @(
    "http://localhost:8080",
    "http://localhost:8081", 
    "http://localhost:8082"
)

$names = @(
    "Main Web Portal",
    "Admin Portal",
    "Seller Portal"
)

Write-Host "`nTesting HTTP connectivity..." -ForegroundColor Cyan

for ($i = 0; $i -lt $urls.Length; $i++) {
    try {
        $response = Invoke-WebRequest -Uri $urls[$i] -Method GET -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "OK: $($names[$i]) - $($urls[$i])" -ForegroundColor Green
        } else {
            Write-Host "WARNING: $($names[$i]) - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "ERROR: $($names[$i]) - $($urls[$i]) not accessible" -ForegroundColor Red
    }
}

Write-Host "`nTesting Docker containers..." -ForegroundColor Cyan
try {
    $containers = docker ps --format "{{.Names}} - {{.Status}}"
    if ($containers) {
        Write-Host "Docker containers running:" -ForegroundColor Green
        $containers | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    } else {
        Write-Host "No Docker containers running" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Docker not available or not running" -ForegroundColor Red
}

Write-Host "`nTesting new features APIs..." -ForegroundColor Cyan

$apiTests = @(
    @{ Name = "Coupons"; Url = "http://localhost:8080/api/coupons" },
    @{ Name = "Inventory"; Url = "http://localhost:8080/api/inventory/dashboard" },
    @{ Name = "Notifications"; Url = "http://localhost:8080/api/notifications/templates" },
    @{ Name = "Delivery"; Url = "http://localhost:8080/api/delivery/zones" }
)

$workingApis = 0
foreach ($test in $apiTests) {
    try {
        $response = Invoke-RestMethod -Uri $test.Url -Method GET -TimeoutSec 5
        Write-Host "OK: $($test.Name) API working" -ForegroundColor Green
        $workingApis++
    } catch {
        Write-Host "ERROR: $($test.Name) API not working" -ForegroundColor Red
    }
}

Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "Working APIs: $workingApis/4" -ForegroundColor Blue

if ($workingApis -eq 4) {
    Write-Host "SUCCESS: All new features are working!" -ForegroundColor Green
} elseif ($workingApis -ge 2) {
    Write-Host "PARTIAL: Some features working, check logs" -ForegroundColor Yellow
} else {
    Write-Host "FAILED: Most features not working, check setup" -ForegroundColor Red
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Open http://localhost:8080 in browser"
Write-Host "2. Test coupon codes: WELCOME10, FREESHIP, SAVE50K"
Write-Host "3. Check admin portal: http://localhost:8081"
Write-Host "4. Check seller portal: http://localhost:8082"

Write-Host "`nTest completed!" -ForegroundColor Green
