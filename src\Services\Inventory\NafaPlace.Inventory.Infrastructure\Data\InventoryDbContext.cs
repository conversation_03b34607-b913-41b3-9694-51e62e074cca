using Microsoft.EntityFrameworkCore;
using NafaPlace.Inventory.Domain.Models;

namespace NafaPlace.Inventory.Infrastructure.Data;

public class InventoryDbContext : DbContext
{
    public InventoryDbContext(DbContextOptions<InventoryDbContext> options) : base(options)
    {
    }

    public DbSet<StockReservation> StockReservations { get; set; }
    public DbSet<StockAlert> StockAlerts { get; set; }
    public DbSet<StockAlertNotification> StockAlertNotifications { get; set; }
    public DbSet<StockMovement> StockMovements { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure StockReservation entity
        modelBuilder.Entity<StockReservation>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProductId).IsRequired();
            entity.Property(e => e.UserId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.SessionId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Quantity).IsRequired();
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.ReservedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.ExpiresAt).IsRequired();
            entity.Property(e => e.OrderId).HasMaxLength(50);
            entity.Property(e => e.Reason).HasMaxLength(200);

            // Indexes for performance
            entity.HasIndex(e => e.ProductId);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.SessionId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.ExpiresAt);
            entity.HasIndex(e => new { e.ProductId, e.Status });
        });

        // Configure StockAlert entity
        modelBuilder.Entity<StockAlert>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProductId).IsRequired();
            entity.Property(e => e.ProductName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.Severity).IsRequired();
            entity.Property(e => e.Message).IsRequired().HasMaxLength(500);
            entity.Property(e => e.CurrentStock).IsRequired();
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsAcknowledged).HasDefaultValue(false);
            entity.Property(e => e.AcknowledgedBy).HasMaxLength(50);
            entity.Property(e => e.SellerId).IsRequired();
            entity.Property(e => e.SellerName).HasMaxLength(100);

            // Indexes for performance
            entity.HasIndex(e => e.ProductId);
            entity.HasIndex(e => e.SellerId);
            entity.HasIndex(e => e.Type);
            entity.HasIndex(e => e.Severity);
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.IsAcknowledged);
            entity.HasIndex(e => new { e.SellerId, e.IsActive });
        });

        // Configure StockAlertNotification entity
        modelBuilder.Entity<StockAlertNotification>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AlertId).IsRequired();
            entity.Property(e => e.UserId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Channel).IsRequired();
            entity.Property(e => e.Recipient).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.ErrorMessage).HasMaxLength(500);
            entity.Property(e => e.RetryCount).HasDefaultValue(0);

            // Indexes for performance
            entity.HasIndex(e => e.AlertId);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.Channel);

            // Foreign key relationship
            entity.HasOne(e => e.Alert)
                  .WithMany(a => a.Notifications)
                  .HasForeignKey(e => e.AlertId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure StockMovement entity
        modelBuilder.Entity<StockMovement>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProductId).IsRequired();
            entity.Property(e => e.ProductName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.Quantity).IsRequired();
            entity.Property(e => e.PreviousStock).IsRequired();
            entity.Property(e => e.NewStock).IsRequired();
            entity.Property(e => e.Reason).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Reference).HasMaxLength(50);
            entity.Property(e => e.UserId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.UserName).HasMaxLength(100);
            entity.Property(e => e.SellerId).IsRequired();
            entity.Property(e => e.Notes).HasMaxLength(500);

            // Indexes for performance
            entity.HasIndex(e => e.ProductId);
            entity.HasIndex(e => e.SellerId);
            entity.HasIndex(e => e.Type);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => new { e.ProductId, e.CreatedAt });
            entity.HasIndex(e => new { e.SellerId, e.CreatedAt });
        });

        // Seed data
        SeedData(modelBuilder);
    }

    private static void SeedData(ModelBuilder modelBuilder)
    {
        // Seed some example stock alerts for testing
        modelBuilder.Entity<StockAlert>().HasData(
            new StockAlert
            {
                Id = 1,
                ProductId = 1,
                ProductName = "iPhone 15 Pro",
                Type = AlertType.LowStock,
                Severity = AlertSeverity.Warning,
                Message = "Stock faible pour iPhone 15 Pro. Stock actuel: 5, Seuil: 10",
                CurrentStock = 5,
                ThresholdValue = 10,
                IsActive = true,
                IsAcknowledged = false,
                SellerId = 1,
                SellerName = "TechStore Guinea",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new StockAlert
            {
                Id = 2,
                ProductId = 2,
                ProductName = "Samsung Galaxy S24",
                Type = AlertType.OutOfStock,
                Severity = AlertSeverity.Emergency,
                Message = "Rupture de stock pour Samsung Galaxy S24",
                CurrentStock = 0,
                IsActive = true,
                IsAcknowledged = false,
                SellerId = 1,
                SellerName = "TechStore Guinea",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        );
    }
}
