using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using NafaPlace.Identity.Application.DTOs.Auth;
using NafaPlace.Identity.Application.DTOs.Role;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Persistence;
using Xunit;

namespace NafaPlace.Identity.IntegrationTests.Controllers;

public class RolesControllerTests : IClassFixture<CustomWebApplicationFactory>
{
    private readonly CustomWebApplicationFactory _factory;
    private readonly HttpClient _client;

    public RolesControllerTests(CustomWebApplicationFactory factory)
    {
        _factory = factory;
        _client = factory.CreateClient();
    }

    private async Task<string> GetAdminTokenAsync()
    {
        // Créer un utilisateur admin
        var adminUser = new User
        {
           
            Email = "<EMAIL>",
            Username = "admin",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin123!"),
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var adminRole = new Role
        {
            Name = "Admin",
            Description = "Administrator role",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var userRole = new UserRole
        {
            UserId = adminUser.Id,
            RoleId = adminRole.Id,
            CreatedAt = DateTime.UtcNow
        };

        using (var scope = _factory.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<IdentityDbContext>();
            context.Users.Add(adminUser);
            context.Roles.Add(adminRole);
            context.UserRoles.Add(userRole);
            await context.SaveChangesAsync();
        }

        // Connecter l'utilisateur admin
        var loginRequest = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "Admin123!"
        };

        var loginContent = new StringContent(
            JsonSerializer.Serialize(loginRequest),
            Encoding.UTF8,
            "application/json");

        var loginResponse = await _client.PostAsync("/api/auth/login", loginContent);
        var loginResult = JsonSerializer.Deserialize<AuthResponse>(
            await loginResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        return loginResult.AccessToken;
    }

    [Fact]
    public async Task CreateRole_WithValidRequest_ShouldReturnSuccess()
    {
        // Arrange
        var token = await GetAdminTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var request = new CreateRoleRequest
        {
            Name = "TestRole",
            Description = "Test Role Description"
        };

        var content = new StringContent(
            JsonSerializer.Serialize(request),
            Encoding.UTF8,
            "application/json");

        // Act
        var response = await _client.PostAsync("/api/roles", content);
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<RoleDto>(
            responseString,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.NotNull(result);
        Assert.Equal(request.Name, result.Name);
        Assert.Equal(request.Description, result.Description);
    }

    [Fact]
    public async Task CreateRole_WithoutAdminRole_ShouldReturnForbidden()
    {
        // Arrange
        var request = new CreateRoleRequest
        {
            Name = "TestRole",
            Description = "Test Role Description"
        };

        var content = new StringContent(
            JsonSerializer.Serialize(request),
            Encoding.UTF8,
            "application/json");

        // Act
        var response = await _client.PostAsync("/api/roles", content);

        // Assert
        Assert.Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }

    [Fact]
    public async Task UpdateRole_WithValidRequest_ShouldReturnSuccess()
    {
        // Arrange
        var token = await GetAdminTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Créer d'abord un rôle
        var createRequest = new CreateRoleRequest
        {
            Name = "RoleToUpdate",
            Description = "Original Description"
        };

        var createContent = new StringContent(
            JsonSerializer.Serialize(createRequest),
            Encoding.UTF8,
            "application/json");

        var createResponse = await _client.PostAsync("/api/roles", createContent);
        var createResult = JsonSerializer.Deserialize<RoleDto>(
            await createResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // Mettre à jour le rôle
        var updateRequest = new UpdateRoleRequest
        {
            Name = "UpdatedRole",
            Description = "Updated Description"
        };

        var updateContent = new StringContent(
            JsonSerializer.Serialize(updateRequest),
            Encoding.UTF8,
            "application/json");

        // Act
        var response = await _client.PutAsync($"/api/roles/{createResult.Id}", updateContent);
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<RoleDto>(
            responseString,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.NotNull(result);
        Assert.Equal(updateRequest.Name, result.Name);
        Assert.Equal(updateRequest.Description, result.Description);
    }

    [Fact]
    public async Task AssignRoleToUser_WithValidIds_ShouldReturnSuccess()
    {
        // Arrange
        var token = await GetAdminTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Créer un utilisateur test
        var registerRequest = new RegisterRequest
        {
            Email = "<EMAIL>",
            Username = "testuser",
            Password = "Password123!",
            PhoneNumber = "1234567890"
        };

        var registerContent = new StringContent(
            JsonSerializer.Serialize(registerRequest),
            Encoding.UTF8,
            "application/json");

        var registerResponse = await _client.PostAsync("/api/auth/register", registerContent);
        var registerResult = JsonSerializer.Deserialize<AuthResponse>(
            await registerResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // Créer un rôle test
        var createRoleRequest = new CreateRoleRequest
        {
            Name = "TestRole",
            Description = "Test Role"
        };

        var createRoleContent = new StringContent(
            JsonSerializer.Serialize(createRoleRequest),
            Encoding.UTF8,
            "application/json");

        var createRoleResponse = await _client.PostAsync("/api/roles", createRoleContent);
        var roleResult = JsonSerializer.Deserialize<RoleDto>(
            await createRoleResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // Act
        var response = await _client.PostAsync(
            $"/api/roles/users/{registerResult.User.Id}/roles/{roleResult.Id}",
            null);

        // Assert
        Assert.Equal(HttpStatusCode.NoContent, response.StatusCode);

        // Vérifier que le rôle a été assigné
        var userRolesResponse = await _client.GetAsync($"/api/roles/users/{registerResult.User.Id}/roles");
        var userRoles = JsonSerializer.Deserialize<List<RoleDto>>(
            await userRolesResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        Assert.Contains(userRoles, r => r.Id == roleResult.Id);
    }

    [Fact]
    public async Task GetAllRoles_ShouldReturnRolesList()
    {
        // Arrange
        var token = await GetAdminTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Créer quelques rôles
        var roles = new[]
        {
            new CreateRoleRequest { Name = "Role1", Description = "Description 1" },
            new CreateRoleRequest { Name = "Role2", Description = "Description 2" }
        };

        foreach (var role in roles)
        {
            var content = new StringContent(
                JsonSerializer.Serialize(role),
                Encoding.UTF8,
                "application/json");

            await _client.PostAsync("/api/roles", content);
        }

        // Act
        var response = await _client.GetAsync("/api/roles");
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<List<RoleDto>>(
            responseString,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.NotNull(result);
        Assert.Contains(result, r => r.Name == "Role1");
        Assert.Contains(result, r => r.Name == "Role2");
    }
}
