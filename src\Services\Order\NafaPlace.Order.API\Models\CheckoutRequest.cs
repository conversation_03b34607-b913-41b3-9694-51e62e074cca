using NafaPlace.Order.Domain;

namespace NafaPlace.Order.API.Models
{
    public class CheckoutRequest
    {
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.CashOnDelivery;
        public ShippingAddressDto? ShippingAddress { get; set; }
        public string? PhoneNumber { get; set; } // Pour Orange Money
    }

    public class ShippingAddressDto
    {
        public string FullName { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
    }

    public class InitiatePaymentRequest
    {
        public string? PhoneNumber { get; set; } // Pour Orange Money
        public string? ReturnUrl { get; set; }
        public string? CancelUrl { get; set; }
    }

    public class UpdatePaymentStatusRequest
    {
        public PaymentStatus PaymentStatus { get; set; }
        public string? TransactionId { get; set; }
    }

    public class UpdateOrderStatusRequest
    {
        public string Status { get; set; } = string.Empty;
        public string? TrackingNumber { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateOrderStatusResponse
    {
        public int Id { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }
}
