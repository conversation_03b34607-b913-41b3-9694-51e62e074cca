using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using NafaPlace.Web;
using NafaPlace.Web.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Configuration des HttpClient pour chaque API
var identityApiUrl = builder.Configuration["ApiEndpoints:IdentityApi"] ?? "http://localhost:5155";
var catalogApiUrl = builder.Configuration["ApiEndpoints:CatalogApi"] ?? "http://localhost:5243";
var cartApiUrl = builder.Configuration["ApiEndpoints:CartApi"] ?? "http://localhost:5003"; // Port for the new Cart API
var orderApiUrl = builder.Configuration["ApiEndpoints:OrderApi"] ?? "http://localhost:5004"; // Port for the new Order API
var reviewApiUrl = builder.Configuration["ApiEndpoints:ReviewApi"] ?? "http://localhost:5006"; // Port for the Reviews API

// HttpClient pour l'API Identity (utilisé par AuthService)
builder.Services.AddScoped<HttpClient>(sp => new HttpClient { BaseAddress = new Uri(identityApiUrl) });

// HttpClient pour l'API Catalog (utilisé par ProductService et CategoryService)
builder.Services.AddScoped<ICategoryService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(catalogApiUrl) };
    return new CategoryService(httpClient);
});

builder.Services.AddScoped<IProductService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(catalogApiUrl) };
    return new ProductService(httpClient);
});

// Services
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddAuthorizationCore();

// Enregistrer CustomAuthStateProvider en premier
builder.Services.AddScoped<CustomAuthStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider =>
    provider.GetRequiredService<CustomAuthStateProvider>());

// Puis enregistrer les services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddSingleton<CartNotificationService>();
builder.Services.AddScoped<ICartService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(cartApiUrl) };
    var notificationService = sp.GetRequiredService<CartNotificationService>();
    return new CartService(httpClient, notificationService);
});
builder.Services.AddScoped<IGuestCartMergeService, GuestCartMergeService>();

builder.Services.AddScoped<IOrderService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(orderApiUrl) };
    return new OrderService(httpClient);
});

builder.Services.AddScoped<IReviewService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(reviewApiUrl) };
    var authStateProvider = sp.GetRequiredService<AuthenticationStateProvider>();
    return new ReviewService(httpClient, authStateProvider);
});

// Récupérer l'URL de l'API Wishlist depuis la configuration
var wishlistApiUrl = builder.Configuration.GetSection("ApiEndpoints")["WishlistApi"] ?? "http://localhost:5008";

builder.Services.AddScoped<IWishlistService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(wishlistApiUrl) };
    var logger = sp.GetRequiredService<ILogger<WishlistService>>();
    return new WishlistService(httpClient, logger);
});

await builder.Build().RunAsync();
