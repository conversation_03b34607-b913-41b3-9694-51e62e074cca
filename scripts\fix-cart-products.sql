-- Script de correction complète pour résoudre les problèmes de panier
-- Ce script s'assure que tous les produits sont correctement configurés

-- =============================================
-- CORRECTION DE LA BASE DE DONNÉES CATALOG
-- =============================================

-- 1. S'assurer que tous les produits ont IsActive = true et du stock
UPDATE "Products" 
SET 
    "IsActive" = true,
    "StockQuantity" = CASE 
        WHEN "StockQuantity" <= 0 THEN 100 
        ELSE "StockQuantity" 
    END,
    "UpdatedAt" = NOW()
WHERE "IsActive" = false OR "StockQuantity" <= 0;

-- 2. S'assurer que tous les produits ont une devise correcte
UPDATE "Products" 
SET 
    "Currency" = 'GNF',
    "UpdatedAt" = NOW()
WHERE "Currency" IS NULL OR "Currency" = '' OR "Currency" = 'XOF';

-- 3. S'assurer que tous les produits ont des prix valides
UPDATE "Products" 
SET 
    "Price" = CASE 
        WHEN "Price" <= 0 THEN 50000 
        ELSE "Price" 
    END,
    "UpdatedAt" = NOW()
WHERE "Price" <= 0;

-- 4. Vérifier et corriger les catégories manquantes
UPDATE "Products" 
SET 
    "CategoryId" = 1,
    "UpdatedAt" = NOW()
WHERE "CategoryId" IS NULL OR "CategoryId" <= 0 
   OR NOT EXISTS (SELECT 1 FROM "Categories" WHERE "Id" = "Products"."CategoryId");

-- 5. Vérifier et corriger les vendeurs manquants
UPDATE "Products" 
SET 
    "SellerId" = 1,
    "UpdatedAt" = NOW()
WHERE "SellerId" IS NULL OR "SellerId" <= 0 
   OR NOT EXISTS (SELECT 1 FROM "Sellers" WHERE "Id" = "Products"."SellerId");

-- =============================================
-- INSERTION DE PRODUITS DE TEST SI NÉCESSAIRE
-- =============================================

-- Insérer des produits de test si la table est vide ou a peu de produits
INSERT INTO "Products" (
    "Name", "Description", "Price", "Currency", "StockQuantity", 
    "CategoryId", "SellerId", "IsActive", "IsFeatured", 
    "Brand", "Model", "Weight", "Dimensions", "Rating",
    "CreatedAt", "UpdatedAt"
)
SELECT * FROM (VALUES
    ('Smartphone Samsung Galaxy A54', 'Smartphone Android avec écran 6.4 pouces, 128GB de stockage', 2500000, 'GNF', 25, 1, 1, true, true, 'Samsung', 'Galaxy A54', 0.2, '15x7x0.8', 4.5, NOW(), NOW()),
    ('Ordinateur Portable HP Pavilion', 'PC portable 15.6 pouces, Intel Core i5, 8GB RAM, 256GB SSD', 8500000, 'GNF', 10, 1, 1, true, false, 'HP', 'Pavilion 15', 2.1, '36x24x2', 4.2, NOW(), NOW()),
    ('Chaussures Nike Air Max', 'Baskets de sport confortables et stylées, taille 42', 850000, 'GNF', 50, 1, 1, true, true, 'Nike', 'Air Max', 0.8, '30x20x12', 4.7, NOW(), NOW()),
    ('Réfrigérateur LG 300L', 'Réfrigérateur double porte, classe énergétique A+, 300 litres', 4200000, 'GNF', 8, 1, 1, true, false, 'LG', 'GR-B300', 65.0, '60x65x170', 4.3, NOW(), NOW()),
    ('Livre Histoire de la Guinée', 'Ouvrage de référence complet sur l''histoire de la Guinée', 75000, 'GNF', 100, 1, 1, true, false, 'Editions Ganndal', 'Histoire', 0.5, '21x15x3', 4.8, NOW(), NOW()),
    ('Montre Casio G-Shock', 'Montre sport étanche et résistante aux chocs', 1200000, 'GNF', 30, 1, 1, true, true, 'Casio', 'G-Shock DW-5600', 0.1, '5x4x1', 4.6, NOW(), NOW()),
    ('Sac à dos Eastpak', 'Sac à dos résistant et spacieux, idéal pour l''école ou le travail', 450000, 'GNF', 75, 1, 1, true, false, 'Eastpak', 'Padded Pak''r', 0.4, '40x30x18', 4.4, NOW(), NOW()),
    ('Télévision Samsung 55 pouces 4K', 'Smart TV 4K UHD, HDR, WiFi intégré, 55 pouces', 6800000, 'GNF', 5, 1, 1, true, true, 'Samsung', 'UE55AU7100', 15.5, '123x71x6', 4.5, NOW(), NOW()),
    ('Téléphone iPhone 13', 'iPhone 13 128GB, écran Super Retina XDR', 7500000, 'GNF', 15, 1, 1, true, true, 'Apple', 'iPhone 13', 0.17, '14.7x7.2x0.8', 4.8, NOW(), NOW()),
    ('Casque Audio Sony WH-1000XM4', 'Casque sans fil avec réduction de bruit active', 2200000, 'GNF', 20, 1, 1, true, false, 'Sony', 'WH-1000XM4', 0.25, '25x20x8', 4.9, NOW(), NOW())
) AS new_products(name, description, price, currency, stock, category_id, seller_id, is_active, is_featured, brand, model, weight, dimensions, rating, created_at, updated_at)
WHERE NOT EXISTS (
    SELECT 1 FROM "Products" WHERE "Name" = new_products.name
);

-- =============================================
-- VÉRIFICATIONS FINALES
-- =============================================

-- Afficher un résumé des produits après correction
SELECT 
    COUNT(*) as "Total_Products",
    COUNT(CASE WHEN "IsActive" = true THEN 1 END) as "Active_Products",
    COUNT(CASE WHEN "StockQuantity" > 0 THEN 1 END) as "Products_In_Stock",
    COUNT(CASE WHEN "IsActive" = true AND "StockQuantity" > 0 THEN 1 END) as "Available_Products"
FROM "Products";

-- Afficher les premiers produits pour vérification
SELECT 
    "Id", "Name", "Price", "Currency", "StockQuantity", "IsActive", "CategoryId", "SellerId"
FROM "Products" 
WHERE "IsActive" = true AND "StockQuantity" > 0
ORDER BY "Id" 
LIMIT 10;

COMMIT;
