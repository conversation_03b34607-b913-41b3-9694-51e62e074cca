@using NafaPlace.Reviews.DTOs
@using NafaPlace.Web.Components.Reviews
@using System.ComponentModel.DataAnnotations
@namespace NafaPlace.Web.Components.Reviews

<div class="review-form">
    <EditForm Model="@_reviewModel" OnValidSubmit="@HandleSubmit">
        <DataAnnotationsValidator />
        
        <div class="mb-3">
            <label class="form-label">Note *</label>
            <div class="d-flex align-items-center">
                <StarRating Rating="_reviewModel.Rating" 
                           IsInteractive="true" 
                           CssClass="large me-3"
                           OnRatingChanged="OnRatingChanged" />
                <span class="text-muted">(@_reviewModel.Rating/5)</span>
            </div>
            <ValidationMessage For="@(() => _reviewModel.Rating)" class="text-danger" />
        </div>

        <div class="mb-3">
            <label for="title" class="form-label">Titre de votre avis *</label>
            <InputText id="title" class="form-control" @bind-Value="_reviewModel.Title" 
                      placeholder="Résumez votre expérience en quelques mots" />
            <ValidationMessage For="@(() => _reviewModel.Title)" class="text-danger" />
        </div>

        <div class="mb-3">
            <label for="comment" class="form-label">Votre commentaire *</label>
            <InputTextArea id="comment" class="form-control" rows="4" @bind-Value="_reviewModel.Comment"
                          placeholder="Partagez votre expérience avec ce produit..." />
            <ValidationMessage For="@(() => _reviewModel.Comment)" class="text-danger" />
            <div class="form-text">@(_reviewModel.Comment?.Length ?? 0)/1000 caractères</div>
        </div>

        @if (ShowVerifiedPurchase)
        {
            <div class="mb-3 form-check">
                <InputCheckbox id="verified" class="form-check-input" @bind-Value="_reviewModel.IsVerifiedPurchase" />
                <label class="form-check-label" for="verified">
                    Cet avis concerne un achat vérifié
                </label>
            </div>
        }

        <div class="d-flex justify-content-end gap-2">
            <button type="button" class="btn btn-secondary" @onclick="HandleCancel">
                Annuler
            </button>
            <button type="submit" class="btn btn-primary" disabled="@_isSubmitting">
                @if (_isSubmitting)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                }
                @(IsEdit ? "Modifier l'avis" : "Publier l'avis")
            </button>
        </div>
    </EditForm>
</div>

@code {
    [Parameter] public int ProductId { get; set; }
    [Parameter] public ReviewDto? ExistingReview { get; set; }
    [Parameter] public bool IsEdit { get; set; } = false;
    [Parameter] public bool ShowVerifiedPurchase { get; set; } = true;
    [Parameter] public EventCallback<CreateReviewRequest> OnSubmit { get; set; }
    [Parameter] public EventCallback<UpdateReviewRequest> OnUpdate { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }

    private ReviewFormModel _reviewModel = new();
    private bool _isSubmitting = false;

    protected override void OnInitialized()
    {
        if (IsEdit && ExistingReview != null)
        {
            _reviewModel = new ReviewFormModel
            {
                Rating = ExistingReview.Rating,
                Title = ExistingReview.Title,
                Comment = ExistingReview.Comment,
                IsVerifiedPurchase = ExistingReview.IsVerifiedPurchase
            };
        }
        else
        {
            _reviewModel = new ReviewFormModel
            {
                Rating = 5,
                IsVerifiedPurchase = ShowVerifiedPurchase
            };
        }
    }

    private async Task OnRatingChanged(int rating)
    {
        _reviewModel.Rating = rating;
        StateHasChanged();
    }

    private async Task HandleSubmit()
    {
        _isSubmitting = true;
        StateHasChanged();

        try
        {
            if (IsEdit)
            {
                var updateRequest = new UpdateReviewRequest
                {
                    Rating = _reviewModel.Rating,
                    Title = _reviewModel.Title,
                    Comment = _reviewModel.Comment
                };
                await OnUpdate.InvokeAsync(updateRequest);
            }
            else
            {
                var createRequest = new CreateReviewRequest
                {
                    ProductId = ProductId,
                    Rating = _reviewModel.Rating,
                    Title = _reviewModel.Title,
                    Comment = _reviewModel.Comment,
                    IsVerifiedPurchase = _reviewModel.IsVerifiedPurchase
                };
                await OnSubmit.InvokeAsync(createRequest);
            }
        }
        finally
        {
            _isSubmitting = false;
            StateHasChanged();
        }
    }

    private async Task HandleCancel()
    {
        await OnCancel.InvokeAsync();
    }

    public class ReviewFormModel
    {
        [Required(ErrorMessage = "La note est obligatoire")]
        [Range(1, 5, ErrorMessage = "La note doit être entre 1 et 5")]
        public int Rating { get; set; } = 5;

        [Required(ErrorMessage = "Le titre est obligatoire")]
        [StringLength(100, ErrorMessage = "Le titre ne peut pas dépasser 100 caractères")]
        public string Title { get; set; } = string.Empty;

        [Required(ErrorMessage = "Le commentaire est obligatoire")]
        [StringLength(1000, ErrorMessage = "Le commentaire ne peut pas dépasser 1000 caractères")]
        public string Comment { get; set; } = string.Empty;

        public bool IsVerifiedPurchase { get; set; }
    }
}

<style>
    .review-form {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .review-form .form-label {
        font-weight: 600;
        color: #495057;
    }

    .review-form .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
</style>
