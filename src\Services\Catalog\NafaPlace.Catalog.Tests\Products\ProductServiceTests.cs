using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Moq;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.Common.Mappings;
using NafaPlace.Catalog.Application.DTOs.Category;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Application.Services;
using NafaPlace.Catalog.Domain.Models;
using NafaPlace.Catalog.Infrastructure.Persistence;
using Xunit;

namespace NafaPlace.Catalog.Tests.Products
{
    public class ProductServiceTests : IDisposable
    {
        private readonly CatalogDbContext _dbContext;
        private readonly IMapper _mapper;
        private readonly ProductService _productService;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Category _testCategory;
        private readonly int _sellerId;

        public ProductServiceTests()
        {
            var options = new DbContextOptionsBuilder<CatalogDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _dbContext = new CatalogDbContext(options);
            
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<MappingProfile>();
            });
            _mapper = mapperConfig.CreateMapper();
            
            // Créer un mock mapper pour les tests qui en ont besoin
            _mockMapper = new Mock<IMapper>();
            
            // Créer une catégorie de test
            _sellerId = new Random().Next();
            _testCategory = new Category
            {
                Id = new Random().Next(),
                Name = "Test Category",
                Description = "Test Category Description",
                IconUrl = "https://example.com/icon.png",
                ImageUrl = "https://example.com/image.png",
                ParentCategoryId = null,
                IsActive = true,
                DisplayOrder = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            _dbContext.Categories.Add(_testCategory);
            _dbContext.SaveChanges();
            
            // Utiliser le mapper réel par défaut
            _productService = new ProductService(_dbContext, _mapper);
        }

        public void Dispose()
        {
            _dbContext.Database.EnsureDeleted();
            _dbContext.Dispose();
        }

        [Fact]
        public async Task CreateProduct_WithValidData_ReturnsProductDto()
        {
            // Arrange
            var createProductRequest = new CreateProductRequest
            {
                Name = "Test Product",
                Description = "Test Description",
                Price = 100.00m,
                Currency = "XOF",
                StockQuantity = 10,
                CategoryId = _testCategory.Id,
                SellerId = _sellerId,
                Brand = "Test Brand",
                Model = "Test Model",
                Dimensions = "10x20x30",
                Weight = 1.5m
            };

            // Act
            var result = await _productService.CreateProductAsync(createProductRequest);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(createProductRequest.Name, result.Name);
            Assert.Equal(createProductRequest.Description, result.Description);
            Assert.Equal(createProductRequest.Price, result.Price);
            
            // Vérifier que le produit existe dans la base de données
            var productInDb = await _dbContext.Products.FirstOrDefaultAsync(p => p.Name == createProductRequest.Name);
            Assert.NotNull(productInDb);
            Assert.Equal(createProductRequest.Name, productInDb.Name);
            Assert.Equal(createProductRequest.Description, productInDb.Description);
            Assert.Equal(createProductRequest.Price, productInDb.Price);
        }

        [Fact]
        public async Task GetProductById_ReturnsProduct()
        {
            // Arrange
            var product = new Product
            {
                Id = new Random().Next(),
                Name = "Test Product",
                Description = "Test Description",
                Price = 99.99m,
                StockQuantity = 10,
                Currency = "XOF",
                CategoryId = _testCategory.Id,
                SellerId = _sellerId,
                Brand = "Test Brand",
                Model = "Test Model",
                Dimensions = "10x20x30",
                Weight = 1.5m,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            await _dbContext.Products.AddAsync(product);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _productService.GetProductByIdAsync(product.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(product.Id, result.Id);
            Assert.Equal(product.Name, result.Name);
            Assert.Equal(product.Description, result.Description);
            Assert.Equal(product.Price, result.Price);
            Assert.Equal(product.StockQuantity, result.StockQuantity);
            Assert.Equal(product.Currency, result.Currency);
            Assert.Equal(product.Brand, result.Brand);
            Assert.Equal(product.Model, result.Model);
            Assert.Equal(product.Dimensions, result.Dimensions);
            Assert.Equal(product.Weight, result.Weight);
        }

        [Fact]
        public async Task UpdateProduct_UpdatesProductProperties()
        {
            // Arrange
            var product = new Product
            {
                Id = new Random().Next(),
                Name = "Test Product",
                Description = "Test Description",
                Price = 99.99m,
                StockQuantity = 10,
                Currency = "XOF",
                CategoryId = _testCategory.Id,
                SellerId = _sellerId,
                Brand = "Test Brand",
                Model = "Test Model",
                Dimensions = "10x20x30",
                Weight = 1.5m,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            await _dbContext.Products.AddAsync(product);
            await _dbContext.SaveChangesAsync();

            var updateRequest = new UpdateProductRequest
            {
                Name = "Updated Product",
                Description = "Updated Description",
                Price = 199.99m,
                StockQuantity = 20,
                Currency = "XOF",
                CategoryId = _testCategory.Id,
                Brand = "Updated Brand",
                Model = "Updated Model",
                Dimensions = "15x25x35",
                Weight = 2.0m,
                IsActive = true,
                IsFeatured = false
            };

            // Act
            var result = await _productService.UpdateProductAsync(product.Id, updateRequest);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(updateRequest.Name, result.Name);
            Assert.Equal(updateRequest.Description, result.Description);
            Assert.Equal(updateRequest.Price, result.Price);
            
            // Vérifier que les modifications ont été enregistrées
            var updatedProduct = await _dbContext.Products.FindAsync(product.Id);
            Assert.NotNull(updatedProduct);
            Assert.Equal(updateRequest.Name, updatedProduct.Name);
        }

        [Fact]
        public async Task DeleteProduct_RemovesProduct()
        {
            // Arrange
            var product = new Product
            {
                Id = new Random().Next(),
                Name = "Test Product",
                Description = "Test Description",
                Price = 99.99m,
                StockQuantity = 10,
                Currency = "XOF",
                CategoryId = _testCategory.Id,
                SellerId = _sellerId,
                Brand = "Test Brand",
                Model = "Test Model",
                Dimensions = "10x20x30",
                Weight = 1.5m,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            await _dbContext.Products.AddAsync(product);
            await _dbContext.SaveChangesAsync();

            // Act
            await _productService.DeleteProductAsync(product.Id);

            // Assert
            var deletedProduct = await _dbContext.Products.FindAsync(product.Id);
            Assert.Null(deletedProduct);
        }

        [Fact]
        public async Task SearchProducts_ReturnsProductList()
        {
            // Arrange
            var products = new List<Product>
            {
                new Product { 
                    Id = new Random().Next(),
                    Name = "Product 1", 
                    CategoryId = _testCategory.Id, 
                    Price = 10.99m,
                    Description = "Test product 1",
                    Currency = "XOF",
                    StockQuantity = 10,
                    SellerId = _sellerId,
                    Brand = "Test Brand 1",
                    Model = "Test Model 1",
                    Dimensions = "10x20x30",
                    Weight = 1.5m,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Product { 
                    Id = new Random().Next(),
                    Name = "Product 2", 
                    CategoryId = _testCategory.Id, 
                    Price = 20.99m,
                    Description = "Test product 2",
                    Currency = "XOF",
                    StockQuantity = 20,
                    SellerId = _sellerId,
                    Brand = "Test Brand 2",
                    Model = "Test Model 2",
                    Dimensions = "15x25x35",
                    Weight = 2.0m,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };
            await _dbContext.Products.AddRangeAsync(products);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _productService.SearchProductsAsync(new ProductSearchDto { CategoryId = _testCategory.Id });

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
        }
    }
}
