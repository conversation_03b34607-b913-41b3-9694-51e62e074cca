{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=5433;Database=NafaPlace.Identity;User Id=postgres;Password=*****************"}, "JwtSettings": {"Secret": "NafaPlaceSecretKey2025ForProductionEnvironment", "Issuer": "NafaPlace", "Audience": "NafaPlaceApi", "TokenLifetimeMinutes": 60}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}