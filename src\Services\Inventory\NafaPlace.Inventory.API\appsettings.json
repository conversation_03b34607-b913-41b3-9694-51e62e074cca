{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=nafaplace_inventory;Username=********;Password=********"}, "JwtSettings": {"SecretKey": "your-super-secret-key-that-is-at-least-32-characters-long", "Issuer": "NafaPlace", "Audience": "NafaPlace", "ExpiryInMinutes": 60}, "ApiSettings": {"NotificationApiUrl": "http://localhost:5008", "CatalogApiUrl": "http://localhost:5243"}}