# 🐳 NafaPlace - Déploiement Docker

## 🚀 Démarrage Rapide

### Prérequis
- Docker Desktop installé et en cours d'exécution
- Docker Compose v2.0+
- 8GB RAM minimum
- 20GB d'espace disque libre

### Déploiement en Une Commande

#### Windows (PowerShell)
```powershell
.\scripts\deploy-docker.ps1
```

#### Linux/Mac (Bash)
```bash
chmod +x scripts/deploy-docker.sh
./scripts/deploy-docker.sh
```

## 📋 Services Déployés

| Service | Port | Description | URL |
|---------|------|-------------|-----|
| **API Gateway** | 5000 | Point d'entrée principal | http://localhost:5000 |
| **Identity API** | 5155 | Authentification | http://localhost:5155 |
| **Catalog API** | 5243 | Gestion produits | http://localhost:5243 |
| **Cart API** | 5003 | Panier d'achat | http://localhost:5003 |
| **Order API** | 5004 | Gestion commandes | http://localhost:5004 |
| **Payment API** | 5005 | Paiements Stripe | http://localhost:5005 |
| **Reviews API** | 5006 | Avis clients | http://localhost:5006 |
| **Notifications API** | 5007 | Notifications | http://localhost:5007 |
| **Web App** | 8080 | Application client | http://localhost:8080 |
| **Admin Portal** | 8081 | Interface admin | http://localhost:8081 |
| **Seller Portal** | 8082 | Interface vendeur | http://localhost:8082 |

## 🗄️ Bases de Données

| Base de Données | Port | Conteneur | Usage |
|-----------------|------|-----------|-------|
| **Catalog DB** | 5432 | nafaplace-catalog-db | Produits, catégories |
| **Identity DB** | 5433 | nafaplace-identity-db | Utilisateurs, rôles |
| **Order DB** | 5434 | nafaplace-order-db | Commandes |
| **Reviews DB** | 5435 | nafaplace-reviews-db | Avis, évaluations |
| **Notifications DB** | 5436 | nafaplace-notifications-db | Notifications |
| **Redis** | 6379 | nafaplace-redis | Cache panier |
| **PgAdmin** | 5050 | nafaplace-pgadmin | Interface DB |

### Connexion PgAdmin
- URL: http://localhost:5050
- Email: <EMAIL>
- Mot de passe: admin

## 💰 Configuration GNF (Franc Guinéen)

### Paramètres Automatiques
- **Devise par défaut**: GNF
- **Livraison gratuite**: > 500,000 GNF
- **Frais de livraison**: 25,000 GNF
- **TVA**: 18%

### Exemples de Prix
- Livre: 75,000 GNF
- Chaussures: 850,000 GNF
- Smartphone: 2,500,000 GNF
- Ordinateur: 8,500,000 GNF

## 🔧 Commandes Utiles

### Gestion des Services
```bash
# Démarrer tous les services
docker-compose up -d

# Arrêter tous les services
docker-compose down

# Redémarrer un service spécifique
docker-compose restart api-gateway

# Reconstruire et redémarrer
docker-compose up --build -d

# Voir les logs
docker-compose logs -f

# Voir les logs d'un service spécifique
docker-compose logs -f catalog-api
```

### Nettoyage
```bash
# Arrêter et supprimer tout (ATTENTION: supprime les données)
docker-compose down -v --remove-orphans

# Supprimer les images
docker rmi $(docker images "lamyas92/nafaplace-*" -q)

# Nettoyage complet Docker
docker system prune -a --volumes
```

## 🧪 Validation du Déploiement

### Script de Validation Automatique
```powershell
# Windows
.\scripts\validate-deployment.ps1

# Linux/Mac
chmod +x scripts/validate-deployment.sh
./scripts/validate-deployment.sh
```

### Tests Manuels Rapides
```bash
# Test API Gateway
curl http://localhost:5000/health

# Test Catalog (produits en GNF)
curl http://localhost:5243/api/products

# Test Cart (calculs GNF)
curl http://localhost:5003/api/cart/test-user/summary

# Test Reviews
curl http://localhost:5006/api/reviews/product/1/summary
```

## 📊 Migration des Données

### Script de Migration XOF → GNF
```bash
# Exécuter le script de migration
docker exec -i nafaplace-catalog-db psql -U postgres -d NafaPlace.Catalog < scripts/migrate-to-gnf.sql
```

### Données de Test
Les services sont pré-configurés avec des données de test en GNF :
- 8 produits d'exemple
- Catégories localisées
- Templates de notifications en français

## 🔍 Monitoring et Logs

### Voir l'État des Services
```bash
# État général
docker-compose ps

# Utilisation des ressources
docker stats

# Logs en temps réel
docker-compose logs -f --tail=100
```

### Logs par Service
```bash
# API Gateway
docker-compose logs -f api-gateway

# Base de données
docker-compose logs -f catalog-db

# Application web
docker-compose logs -f web-app
```

## 🛠️ Dépannage

### Problèmes Courants

#### Services qui ne démarrent pas
```bash
# Vérifier les logs
docker-compose logs service-name

# Redémarrer le service
docker-compose restart service-name

# Reconstruire l'image
docker-compose up --build -d service-name
```

#### Problèmes de Base de Données
```bash
# Vérifier la connexion PostgreSQL
docker exec nafaplace-catalog-db pg_isready -U postgres

# Se connecter à la base
docker exec -it nafaplace-catalog-db psql -U postgres -d NafaPlace.Catalog

# Vérifier Redis
docker exec nafaplace-redis redis-cli ping
```

#### Problèmes de Réseau
```bash
# Vérifier le réseau Docker
docker network ls
docker network inspect nafaplace_nafaplace-network

# Recréer le réseau
docker-compose down
docker-compose up -d
```

### Ports Occupés
Si des ports sont déjà utilisés, modifiez le `docker-compose.yml` :
```yaml
ports:
  - "5001:80"  # Au lieu de 5000:80
```

## 🔐 Sécurité

### Variables d'Environnement
Les secrets sont configurés dans `docker-compose.yml` :
- Clés JWT
- Clés Stripe
- Mots de passe DB

### Production
Pour la production, utilisez :
- Variables d'environnement externes
- Docker Secrets
- Certificats SSL/TLS

## 📈 Performance

### Optimisations
- Images multi-stage pour réduire la taille
- Cache Docker pour builds rapides
- Health checks pour la fiabilité
- Restart policies automatiques

### Monitoring
- Logs centralisés
- Health checks sur tous les services
- Métriques de performance

## 🌍 Localisation Guinéenne

### Adaptations Culturelles
- Timezone: Africa/Conakry
- Locale: fr_GN.UTF-8
- Devise: GNF
- Format numérique français

### Support Multi-Langues
Prêt pour l'extension :
- Français (par défaut)
- Anglais
- Langues locales

## 🚀 Prochaines Étapes

1. **Tester toutes les fonctionnalités**
2. **Configurer les paiements Stripe**
3. **Personnaliser les données**
4. **Déployer en production**

---

## 📞 Support

Pour toute question ou problème :
1. Consultez les logs : `docker-compose logs -f`
2. Vérifiez le guide de test : `TESTING_GUIDE.md`
3. Utilisez le script de validation : `validate-deployment.ps1`

**Votre plateforme e-commerce guinéenne est maintenant prête ! 🇬🇳✨**
