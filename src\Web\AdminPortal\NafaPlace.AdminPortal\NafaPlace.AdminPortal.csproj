<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <BlazorWebAssemblyLoadAllGlobalizationData>false</BlazorWebAssemblyLoadAllGlobalizationData>
    <PublishTrimmed>false</PublishTrimmed>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.0-preview.5.24306.11" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.0-preview.5.24306.11" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.0-preview.5.24306.11" PrivateAssets="all" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0-preview.5.24306.7" />
    <PackageReference Include="System.Net.Http.Json" Version="9.0.0-preview.5.24306.7" />
  </ItemGroup>

</Project>
