namespace NafaPlace.Web.Models.Cart
{
    public class CartItemDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal Price { get; set; }

        // Propriété de compatibilité avec l'API Cart
        public decimal UnitPrice
        {
            get => Price;
            set => Price = value;
        }

        public int Quantity { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public int? VariantId { get; set; }
        public string? VariantName { get; set; }

        // Propriété calculée pour le total de la ligne
        public decimal LineTotal => Price * Quantity;
    }
}
