using System.Security.Claims;
using NafaPlace.Identity.Domain.Models;

namespace NafaPlace.Identity.Application.Common.Interfaces;

public interface IJwtService
{
    string GenerateAccessToken(int userId, string username, IEnumerable<string> roles);
    string GenerateRefreshToken();
    ClaimsPrincipal GetPrincipalFromExpiredToken(string token);
    DateTime GetTokenExpirationTime(string token);
    IEnumerable<Claim> GetClaimsFromToken(string token);
}
