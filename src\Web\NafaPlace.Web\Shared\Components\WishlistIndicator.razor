@using NafaPlace.Web.Services
@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Authorization
@inject IWishlistService WishlistService
@inject IJSRuntime JSRuntime
@implements IDisposable

<a href="/wishlist" class="action-item">
    <i class="bi bi-heart action-icon"></i>
    <span class="action-label">Favoris</span>
    <span class="action-badge">@_itemCount</span>
</a>

@code {
    private int _itemCount = 0;
    private string? _userId;
    private Timer? _refreshTimer;

    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            if (AuthenticationStateTask != null)
            {
                var authState = await AuthenticationStateTask;
                var user = authState.User;

                if (user.Identity?.IsAuthenticated == true)
                {
                    _userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                    Console.WriteLine($"🔍 DEBUG WishlistIndicator: Utilisateur authentifié avec ID: {_userId}");
                }
                else
                {
                    Console.WriteLine("🔍 DEBUG WishlistIndicator: Utilisateur non authentifié");
                    _userId = await GetOrCreateGuestUserId();
                    Console.WriteLine($"🔍 DEBUG WishlistIndicator: ID invité créé: {_userId}");
                }
            }
            else
            {
                Console.WriteLine("⚠️ DEBUG WishlistIndicator: AuthenticationStateTask est null");
                _userId = await GetOrCreateGuestUserId();
            }

            await UpdateWishlistCount();

            // Mettre à jour le compteur toutes les 60 secondes
            _refreshTimer = new Timer(async _ => await UpdateWishlistCount(), null, TimeSpan.Zero, TimeSpan.FromSeconds(60));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'initialisation du WishlistIndicator: {ex.Message}");
        }
    }

    private async Task UpdateWishlistCount()
    {
        Console.WriteLine($"🔍 DEBUG WishlistIndicator: UpdateWishlistCount appelé - UserId: {_userId}");

        if (string.IsNullOrEmpty(_userId))
        {
            // Essayer de récupérer l'ID invité
            _userId = await GetOrCreateGuestUserId();
            Console.WriteLine($"🔍 DEBUG WishlistIndicator: ID invité récupéré: {_userId}");
        }

        try
        {
            Console.WriteLine($"📡 DEBUG WishlistIndicator: Appel GetWishlistItemCountAsync...");
            var count = await WishlistService.GetWishlistItemCountAsync(_userId);

            Console.WriteLine($"📊 DEBUG WishlistIndicator: Ancien count: {_itemCount}, Nouveau count: {count}");

            if (count != _itemCount)
            {
                _itemCount = count;
                Console.WriteLine($"✅ DEBUG WishlistIndicator: Mise à jour du compteur vers {_itemCount}");
                await InvokeAsync(StateHasChanged);
            }
            else
            {
                Console.WriteLine($"ℹ️ DEBUG WishlistIndicator: Pas de changement, compteur reste à {_itemCount}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG WishlistIndicator: Erreur complète: {ex}");
            Console.WriteLine($"Erreur lors de la mise à jour du compteur de wishlist: {ex.Message}");
        }
    }

    public async Task RefreshWishlistCount()
    {
        await UpdateWishlistCount();
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Guid.NewGuid():N}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }

    public void Dispose()
    {
        _refreshTimer?.Dispose();
    }
}
