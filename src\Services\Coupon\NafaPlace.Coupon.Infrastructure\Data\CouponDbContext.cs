using Microsoft.EntityFrameworkCore;
using NafaPlace.Coupon.Domain.Models;
using System.Text.Json;

namespace NafaPlace.Coupon.Infrastructure.Data;

public class CouponDbContext : DbContext
{
    public CouponDbContext(DbContextOptions<CouponDbContext> options) : base(options)
    {
    }

    public DbSet<Domain.Models.Coupon> Coupons { get; set; }
    public DbSet<CouponUsage> CouponUsages { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Coupon entity
        modelBuilder.Entity<Domain.Models.Coupon>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.Value).IsRequired().HasColumnType("decimal(18,2)");
            entity.Property(e => e.MinimumOrderAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.MaximumDiscountAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.StartDate).IsRequired();
            entity.Property(e => e.EndDate).IsRequired();
            entity.Property(e => e.UsageCount).HasDefaultValue(0);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Currency).IsRequired().HasMaxLength(3).HasDefaultValue("GNF");
            entity.Property(e => e.ApplicableToAllProducts).HasDefaultValue(true);
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity.Property(e => e.UpdatedBy).HasMaxLength(50);

            // Configure JSON columns for lists
            entity.Property(e => e.ApplicableProductIds)
                  .HasConversion(
                      v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => v == null ? null : JsonSerializer.Deserialize<List<int>>(v, (JsonSerializerOptions?)null));

            entity.Property(e => e.ApplicableCategoryIds)
                  .HasConversion(
                      v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => v == null ? null : JsonSerializer.Deserialize<List<int>>(v, (JsonSerializerOptions?)null));

            entity.Property(e => e.ApplicableSellerIds)
                  .HasConversion(
                      v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => v == null ? null : JsonSerializer.Deserialize<List<int>>(v, (JsonSerializerOptions?)null));

            entity.Property(e => e.ExcludedProductIds)
                  .HasConversion(
                      v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => v == null ? null : JsonSerializer.Deserialize<List<int>>(v, (JsonSerializerOptions?)null));

            entity.Property(e => e.ExcludedCategoryIds)
                  .HasConversion(
                      v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => v == null ? null : JsonSerializer.Deserialize<List<int>>(v, (JsonSerializerOptions?)null));

            entity.Property(e => e.ExcludedSellerIds)
                  .HasConversion(
                      v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => v == null ? null : JsonSerializer.Deserialize<List<int>>(v, (JsonSerializerOptions?)null));

            // Indexes for performance
            entity.HasIndex(e => e.Code).IsUnique();
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.StartDate);
            entity.HasIndex(e => e.EndDate);
            entity.HasIndex(e => new { e.IsActive, e.StartDate, e.EndDate });
        });

        // Configure CouponUsage entity
        modelBuilder.Entity<CouponUsage>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.CouponId).IsRequired();
            entity.Property(e => e.UserId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.OrderId).HasMaxLength(50);
            entity.Property(e => e.DiscountAmount).IsRequired().HasColumnType("decimal(18,2)");
            entity.Property(e => e.Currency).IsRequired().HasMaxLength(3).HasDefaultValue("GNF");
            entity.Property(e => e.UsedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            // Indexes for performance
            entity.HasIndex(e => e.CouponId);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.OrderId);
            entity.HasIndex(e => e.UsedAt);
            entity.HasIndex(e => new { e.CouponId, e.UserId });

            // Foreign key relationship
            entity.HasOne(e => e.Coupon)
                  .WithMany(c => c.Usages)
                  .HasForeignKey(e => e.CouponId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Seed data
        SeedData(modelBuilder);
    }

    private static void SeedData(ModelBuilder modelBuilder)
    {
        // Seed some example coupons
        modelBuilder.Entity<Domain.Models.Coupon>().HasData(
            new Domain.Models.Coupon
            {
                Id = 1,
                Code = "WELCOME10",
                Name = "Bienvenue - 10% de réduction",
                Description = "10% de réduction pour les nouveaux clients",
                Type = CouponType.Percentage,
                Value = 10,
                MinimumOrderAmount = 50000,
                MaximumDiscountAmount = 100000,
                StartDate = DateTime.UtcNow.AddDays(-30),
                EndDate = DateTime.UtcNow.AddDays(365),
                UsageLimit = 1000,
                UsageLimitPerUser = 1,
                IsActive = true,
                Currency = "GNF",
                ApplicableToAllProducts = true,
                CreatedBy = "System",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Domain.Models.Coupon
            {
                Id = 2,
                Code = "FREESHIP",
                Name = "Livraison gratuite",
                Description = "Livraison gratuite pour toute commande",
                Type = CouponType.FreeShipping,
                Value = 25000,
                MinimumOrderAmount = 100000,
                StartDate = DateTime.UtcNow.AddDays(-7),
                EndDate = DateTime.UtcNow.AddDays(30),
                UsageLimit = 500,
                IsActive = true,
                Currency = "GNF",
                ApplicableToAllProducts = true,
                CreatedBy = "System",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Domain.Models.Coupon
            {
                Id = 3,
                Code = "SAVE50K",
                Name = "Économisez 50,000 GNF",
                Description = "50,000 GNF de réduction sur votre commande",
                Type = CouponType.FixedAmount,
                Value = 50000,
                MinimumOrderAmount = 200000,
                StartDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow.AddDays(14),
                UsageLimit = 100,
                UsageLimitPerUser = 1,
                IsActive = true,
                Currency = "GNF",
                ApplicableToAllProducts = true,
                CreatedBy = "System",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        );
    }
}
