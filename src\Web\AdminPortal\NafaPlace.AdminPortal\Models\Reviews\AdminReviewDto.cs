namespace NafaPlace.AdminPortal.Models.Reviews;

public class AdminReviewDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductImageUrl { get; set; } = string.Empty;
    public string SellerId { get; set; } = string.Empty;
    public string SellerName { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public int Rating { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public bool? IsApproved { get; set; }
    public bool IsVerifiedPurchase { get; set; }
    public int HelpfulCount { get; set; }
    public int ReportCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovedBy { get; set; }
    public string? RejectionReason { get; set; }
}

public class AdminReviewStatsDto
{
    public int TotalReviews { get; set; }
    public int PendingReviews { get; set; }
    public int ApprovedReviews { get; set; }
    public int RejectedReviews { get; set; }
    public int ReportedReviews { get; set; }
    public double AverageRating { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
    public Dictionary<string, int> ReviewsByPeriod { get; set; } = new();
}

public class AdminReviewFilterRequest
{
    public string? Status { get; set; }
    public int? Rating { get; set; }
    public bool? IsVerifiedPurchase { get; set; }
    public string? DateFilter { get; set; }
    public string? SearchTerm { get; set; }
    public string? SellerId { get; set; }
    public int? ProductId { get; set; }
    public bool? HasReports { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 15;
}

public class AdminReviewsPagedResponse
{
    public List<AdminReviewDto> Reviews { get; set; } = new();
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
}

public class ReviewModerationRequest
{
    public int ReviewId { get; set; }
    public bool IsApproved { get; set; }
    public string? Reason { get; set; }
    public string? AdminNotes { get; set; }
}

public class BulkReviewModerationRequest
{
    public List<int> ReviewIds { get; set; } = new();
    public bool IsApproved { get; set; }
    public string? Reason { get; set; }
    public string? AdminNotes { get; set; }
}

public class ReviewReportDto
{
    public int Id { get; set; }
    public int ReviewId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsResolved { get; set; }
    public string? Resolution { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? ResolvedBy { get; set; }
}

public class ReviewAnalyticsDto
{
    public Dictionary<string, int> ReviewsByMonth { get; set; } = new();
    public Dictionary<string, double> AverageRatingByMonth { get; set; } = new();
    public Dictionary<string, int> ReviewsByCategory { get; set; } = new();
    public Dictionary<string, int> TopReviewedProducts { get; set; } = new();
    public Dictionary<string, int> MostActiveReviewers { get; set; } = new();
    public Dictionary<string, int> ReviewModerationStats { get; set; } = new();
}
