using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Domain.Models;

namespace NafaPlace.Inventory.Application.Interfaces;

public interface IInventoryRepository
{
    Task<InventoryDashboardDto> GetInventoryDashboardAsync(int? sellerId = null);
    Task<List<StockAlertDto>> GetActiveAlertsAsync(int? sellerId = null);
    Task<bool> AcknowledgeAlertAsync(int alertId, string acknowledgedBy);
    Task<List<StockMovementDto>> GetProductMovementsAsync(int productId, int page, int pageSize);
    Task<List<StockMovementDto>> GetSellerMovementsAsync(int sellerId, int page, int pageSize);
    Task<List<TopProductDto>> GetTopSellingProductsAsync(int? sellerId, int count);
    Task<List<TopProductDto>> GetLowStockProductsAsync(int? sellerId, int threshold);
    Task<List<StockReservationDto>> GetActiveReservationsAsync(int? sellerId = null);
    Task<bool> ReleaseReservationAsync(int reservationId, string reason);
    Task RecalculateStockLevelsAsync();
    Task<int> CleanupExpiredReservationsAsync();
}
