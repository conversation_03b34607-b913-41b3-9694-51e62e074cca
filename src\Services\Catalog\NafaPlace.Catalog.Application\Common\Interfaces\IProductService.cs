using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NafaPlace.Catalog.Application.DTOs;
using NafaPlace.Catalog.Application.DTOs.Product;

namespace NafaPlace.Catalog.Application.Common.Interfaces
{
    public interface IProductService
    {
        Task<ProductDto> CreateProductAsync(CreateProductRequest request);
        Task<ProductDto> UpdateProductAsync(int id, UpdateProductRequest request);
        Task<bool> DeleteProductAsync(int id);
        Task<ProductDto> GetProductByIdAsync(int id);
        Task<ProductDto> GetProductByIdForManagementAsync(int id); // Pour les portails admin et vendeur
        Task<IEnumerable<ProductDto>> GetAllProductsAsync();
        Task<PagedResultDto<ProductDto>> SearchProductsAsync(ProductSearchDto searchDto);
        Task<PagedResultDto<ProductDto>> SearchProductsForManagementAsync(ProductSearchDto searchDto); // Pour les portails de gestion
        Task<ProductDto> AddProductImageAsync(int productId, CreateProductImageRequest request);
        Task<ProductDto> DeleteProductImageAsync(int productId, int imageId);
        Task<ProductDto> AddProductVariantAsync(int productId, CreateProductVariantRequest request);
        Task<ProductDto> UpdateProductVariantAsync(int productId, int variantId, UpdateProductVariantRequest request);
        Task<ProductDto> DeleteProductVariantAsync(int productId, int variantId);
        
        // Méthodes additionnelles utilisées dans le contrôleur
        Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(int categoryId);
        Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(int categoryId, int page, int pageSize);
        Task<IEnumerable<ProductDto>> GetFeaturedProductsAsync(int count);
        Task<IEnumerable<ProductDto>> GetNewProductsAsync(int count);
        Task<IEnumerable<ProductDto>> GetRelatedProductsAsync(int productId, int count);

        // Méthodes d'approbation des produits
        Task<ProductDto> ApproveProductAsync(int id, string approvedBy);
        Task<ProductDto> RejectProductAsync(int id, string rejectionReason, string rejectedBy);

        // Méthodes pour l'inventaire
        Task<IEnumerable<ProductDto>> GetProductsBySeller(int? sellerId);
    }
}
