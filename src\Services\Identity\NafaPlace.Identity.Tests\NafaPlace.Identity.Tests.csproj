﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NafaPlace.Identity.API\NafaPlace.Identity.API.csproj" />
    <ProjectReference Include="..\NafaPlace.Identity.Application\NafaPlace.Identity.Application.csproj" />
    <ProjectReference Include="..\NafaPlace.Identity.Domain\NafaPlace.Identity.Domain.csproj" />
    <ProjectReference Include="..\NafaPlace.Identity.Infrastructure\NafaPlace.Identity.Infrastructure.csproj" />
  </ItemGroup>

</Project>
