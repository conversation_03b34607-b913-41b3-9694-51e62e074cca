using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.DTOs.Role;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Data;
using NafaPlace.Identity.Infrastructure.Services;
using Xunit;

namespace NafaPlace.Identity.UnitTests.Services;

public class RoleServiceTests
{
    private readonly DbContextOptions<IdentityDbContext> _dbContextOptions;

    public RoleServiceTests()
    {
        _dbContextOptions = new DbContextOptionsBuilder<IdentityDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
    }

    [Fact]
    public async Task CreateRoleAsync_WithValidRequest_ShouldCreateRole()
    {
        // Arrange
        var request = new CreateRoleRequest
        {
            Name = "TestRole",
            Description = "Test Role Description"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        var service = new RoleService(context);

        // Act
        var result = await service.CreateRoleAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(request.Name, result.Name);
        Assert.Equal(request.Description, result.Description);

        var roleInDb = await context.Roles.FirstOrDefaultAsync(r => r.Name == request.Name);
        Assert.NotNull(roleInDb);
        Assert.Equal(request.Description, roleInDb.Description);
    }

    [Fact]
    public async Task CreateRoleAsync_WithDuplicateName_ShouldThrowException()
    {
        // Arrange
        var existingRole = new Role
        {
            Id = Guid.NewGuid(),
            Name = "TestRole",
            Description = "Existing Role",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Roles.Add(existingRole);
        await context.SaveChangesAsync();

        var request = new CreateRoleRequest
        {
            Name = "TestRole",
            Description = "New Role"
        };

        var service = new RoleService(context);

        // Act & Assert
        await Assert.ThrowsAsync<AuthenticationException>(() => service.CreateRoleAsync(request));
    }

    [Fact]
    public async Task UpdateRoleAsync_WithValidRequest_ShouldUpdateRole()
    {
        // Arrange
        var role = new Role
        {
            Id = Guid.NewGuid(),
            Name = "TestRole",
            Description = "Original Description",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Roles.Add(role);
        await context.SaveChangesAsync();

        var request = new UpdateRoleRequest
        {
            Name = "UpdatedRole",
            Description = "Updated Description"
        };

        var service = new RoleService(context);

        // Act
        var result = await service.UpdateRoleAsync(role.Id, request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(request.Name, result.Name);
        Assert.Equal(request.Description, result.Description);

        var updatedRole = await context.Roles.FindAsync(role.Id);
        Assert.NotNull(updatedRole);
        Assert.Equal(request.Name, updatedRole.Name);
        Assert.Equal(request.Description, updatedRole.Description);
    }

    [Fact]
    public async Task AssignRoleToUserAsync_WithValidIds_ShouldAssignRole()
    {
        // Arrange
        var user = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = "hash",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true
        };

        var role = new Role
        {
            Id = Guid.NewGuid(),
            Name = "TestRole",
            Description = "Test Role",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        context.Roles.Add(role);
        await context.SaveChangesAsync();

        var service = new RoleService(context);

        // Act
        await service.AssignRoleToUserAsync(user.Id, role.Id);

        // Assert
        var userRole = await context.UserRoles
            .FirstOrDefaultAsync(ur => ur.UserId == user.Id && ur.RoleId == role.Id);
        Assert.NotNull(userRole);
    }

    [Fact]
    public async Task GetUserRolesAsync_WithValidUser_ShouldReturnRoles()
    {
        // Arrange
        var user = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = "hash",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true
        };

        var role1 = new Role
        {
            Id = Guid.NewGuid(),
            Name = "Role1",
            Description = "Role 1",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var role2 = new Role
        {
            Id = Guid.NewGuid(),
            Name = "Role2",
            Description = "Role 2",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var userRole1 = new UserRole
        {
            UserId = user.Id,
            RoleId = role1.Id,
            CreatedAt = DateTime.UtcNow
        };

        var userRole2 = new UserRole
        {
            UserId = user.Id,
            RoleId = role2.Id,
            CreatedAt = DateTime.UtcNow
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        context.Roles.AddRange(role1, role2);
        context.UserRoles.AddRange(userRole1, userRole2);
        await context.SaveChangesAsync();

        var service = new RoleService(context);

        // Act
        var roles = await service.GetUserRolesAsync(user.Id);

        // Assert
        Assert.NotNull(roles);
        Assert.Equal(2, roles.Count());
        Assert.Contains(roles, r => r.Name == "Role1");
        Assert.Contains(roles, r => r.Name == "Role2");
    }
}
