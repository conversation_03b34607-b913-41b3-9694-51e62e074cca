using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.Extensions.Configuration;
using Moq;
using NafaPlace.Identity.Application.Services;
using NafaPlace.Identity.Domain.Models;
using Xunit;

namespace NafaPlace.Identity.Tests.Token
{
    public class JwtServiceTests
    {
        private readonly Mock<IConfiguration> _configurationMock;
        private readonly JwtService _jwtService;

        public JwtServiceTests()
        {
            _configurationMock = new Mock<IConfiguration>();
            _configurationMock.Setup(x => x["Jwt:Key"]).Returns("votre_clé_secrète_très_longue_et_sécurisée_pour_les_tests");
            _configurationMock.Setup(x => x["Jwt:Issuer"]).Returns("NafaPlace");
            _configurationMock.Setup(x => x["Jwt:Audience"]).Returns("NafaPlaceUsers");
            _configurationMock.Setup(x => x["Jwt:DurationInMinutes"]).Returns("60");

            _jwtService = new JwtService(_configurationMock.Object);
        }

        [Fact]
        public void GenerateToken_ReturnsValidJwtToken()
        {
            // Arrange
            var user = new ApplicationUser
            {
                Id = "user123",
                Email = "<EMAIL>",
                UserName = "testuser",
                FirstName = "Test",
                LastName = "User"
            };

            // Act
            var token = _jwtService.GenerateToken(user);

            // Assert
            Assert.NotNull(token);
            Assert.NotEmpty(token);

            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadJwtToken(token);

            Assert.Equal("NafaPlace", jwtToken.Issuer);
            Assert.Equal("NafaPlaceUsers", jwtToken.Audiences.First());
            Assert.Contains(jwtToken.Claims, c => c.Type == ClaimTypes.NameIdentifier && c.Value == user.Id);
            Assert.Contains(jwtToken.Claims, c => c.Type == ClaimTypes.Email && c.Value == user.Email);
            Assert.Contains(jwtToken.Claims, c => c.Type == ClaimTypes.Name && c.Value == user.UserName);
        }

        [Fact]
        public void ValidateToken_WithValidToken_ReturnsTrue()
        {
            // Arrange
            var user = new ApplicationUser
            {
                Id = "user123",
                Email = "<EMAIL>",
                UserName = "testuser"
            };
            var token = _jwtService.GenerateToken(user);

            // Act
            var isValid = _jwtService.ValidateToken(token);

            // Assert
            Assert.True(isValid);
        }

        [Fact]
        public void ValidateToken_WithInvalidToken_ReturnsFalse()
        {
            // Arrange
            var invalidToken = "invalid.jwt.token";

            // Act
            var isValid = _jwtService.ValidateToken(invalidToken);

            // Assert
            Assert.False(isValid);
        }

        [Fact]
        public void ValidateToken_WithExpiredToken_ReturnsFalse()
        {
            // Arrange
            _configurationMock.Setup(x => x["Jwt:DurationInMinutes"]).Returns("0");
            var jwtService = new JwtService(_configurationMock.Object);
            
            var user = new ApplicationUser
            {
                Id = "user123",
                Email = "<EMAIL>",
                UserName = "testuser"
            };
            var token = jwtService.GenerateToken(user);

            // Act
            var isValid = jwtService.ValidateToken(token);

            // Assert
            Assert.False(isValid);
        }
    }
}
