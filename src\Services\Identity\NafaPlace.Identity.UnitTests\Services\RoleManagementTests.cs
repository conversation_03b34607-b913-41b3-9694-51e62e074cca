using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.DTOs.Role;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Persistence;
using NafaPlace.Identity.Infrastructure.Services;
using Xunit;

namespace NafaPlace.Identity.UnitTests.Services;

public class RoleManagementTests
{
    private readonly DbContextOptions<IdentityDbContext> _dbContextOptions;

    public RoleManagementTests()
    {
        _dbContextOptions = new DbContextOptionsBuilder<IdentityDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
    }

    [Fact]
    public async Task RemoveRoleFromUserAsync_WithValidIds_ShouldRemoveRole()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var roleId = Guid.NewGuid();

        var user = new User
        {
            Id = userId,
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = "hash",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User,Admin"
        };

        var role = new Role
        {
            Id = roleId,
            Name = "Admin",
            Description = "Administrator role",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var userRole = new UserRole
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            RoleId = roleId,
            CreatedAt = DateTime.UtcNow
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        context.Roles.Add(role);
        context.UserRoles.Add(userRole);
        await context.SaveChangesAsync();

        var roleService = new RoleService(context);

        // Act
        var result = await roleService.RemoveRoleFromUserAsync(userId, roleId);

        // Assert
        Assert.True(result);

        // Verify in database
        var userRoleInDb = await context.UserRoles.FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId);
        Assert.Null(userRoleInDb);

        // Verify user's Roles string was updated
        var updatedUser = await context.Users.FindAsync(userId);
        Assert.NotNull(updatedUser);
        Assert.Equal("User", updatedUser.Roles);
    }

    [Fact]
    public async Task RemoveRoleFromUserAsync_WithInvalidIds_ShouldThrowNotFoundException()
    {
        // Arrange
        var invalidUserId = Guid.NewGuid();
        var invalidRoleId = Guid.NewGuid();

        await using var context = new IdentityDbContext(_dbContextOptions);
        var roleService = new RoleService(context);

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => roleService.RemoveRoleFromUserAsync(invalidUserId, invalidRoleId));
    }

    [Fact]
    public async Task GetAllRolesAsync_ShouldReturnAllRoles()
    {
        // Arrange
        var roles = new List<Role>
        {
            new Role
            {
                Id = Guid.NewGuid(),
                Name = "User",
                Description = "Regular user role",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Role
            {
                Id = Guid.NewGuid(),
                Name = "Admin",
                Description = "Administrator role",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Role
            {
                Id = Guid.NewGuid(),
                Name = "Seller",
                Description = "Seller role",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Roles.AddRange(roles);
        await context.SaveChangesAsync();

        var roleService = new RoleService(context);

        // Act
        var result = await roleService.GetAllRolesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(roles.Count, result.Count());
        foreach (var role in roles)
        {
            Assert.Contains(result, r => r.Id == role.Id && r.Name == role.Name);
        }
    }

    [Fact]
    public async Task DeleteRoleAsync_WithValidId_ShouldDeleteRole()
    {
        // Arrange
        var roleId = Guid.NewGuid();
        var role = new Role
        {
            Id = roleId,
            Name = "TestRole",
            Description = "Test role description",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Roles.Add(role);
        await context.SaveChangesAsync();

        var roleService = new RoleService(context);

        // Act
        var result = await roleService.DeleteRoleAsync(roleId);

        // Assert
        Assert.True(result);

        // Verify in database
        var roleInDb = await context.Roles.FindAsync(roleId);
        Assert.Null(roleInDb);
    }

    [Fact]
    public async Task DeleteRoleAsync_WithInvalidId_ShouldThrowNotFoundException()
    {
        // Arrange
        var invalidRoleId = Guid.NewGuid();

        await using var context = new IdentityDbContext(_dbContextOptions);
        var roleService = new RoleService(context);

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => roleService.DeleteRoleAsync(invalidRoleId));
    }

    [Fact]
    public async Task DeleteRoleAsync_WithAssignedUsers_ShouldThrowValidationException()
    {
        // Arrange
        var roleId = Guid.NewGuid();
        var userId = Guid.NewGuid();

        var role = new Role
        {
            Id = roleId,
            Name = "TestRole",
            Description = "Test role description",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var user = new User
        {
            Id = userId,
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = "hash",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "TestRole"
        };

        var userRole = new UserRole
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            RoleId = roleId,
            CreatedAt = DateTime.UtcNow
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Roles.Add(role);
        context.Users.Add(user);
        context.UserRoles.Add(userRole);
        await context.SaveChangesAsync();

        var roleService = new RoleService(context);

        // Act & Assert
        await Assert.ThrowsAsync<ValidationException>(() => roleService.DeleteRoleAsync(roleId));
    }
}
