# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Web/AdminPortal/NafaPlace.AdminPortal/NafaPlace.AdminPortal.csproj", "Web/AdminPortal/NafaPlace.AdminPortal/"]
RUN dotnet restore "Web/AdminPortal/NafaPlace.AdminPortal/NafaPlace.AdminPortal.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Web/AdminPortal/NafaPlace.AdminPortal/", "Web/AdminPortal/NafaPlace.AdminPortal/"]
WORKDIR "/src/Web/AdminPortal/NafaPlace.AdminPortal"
RUN dotnet build "NafaPlace.AdminPortal.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
WORKDIR "/src/Web/AdminPortal/NafaPlace.AdminPortal"

# Clean and restore before publish
RUN dotnet clean "NafaPlace.AdminPortal.csproj" && \
    dotnet restore "NafaPlace.AdminPortal.csproj"

# Force clean and restore before publish
RUN dotnet clean "NafaPlace.AdminPortal.csproj" && \
    dotnet restore "NafaPlace.AdminPortal.csproj" --force

# Publish with specific Blazor WebAssembly settings - Disable service worker to fix SRI issues
RUN dotnet publish "NafaPlace.AdminPortal.csproj" \
    -c Release \
    -o /app/publish \
    /p:UseAppHost=false \
    /p:PublishTrimmed=false \
    /p:BlazorWebAssemblyLoadAllGlobalizationData=false \
    /p:RunAOTCompilation=false \
    /p:BlazorEnableCompression=false \
    /p:ServiceWorkerAssetsManifest= \
    /p:BlazorCacheBootResources=false \
    --verbosity detailed

# Clean up any service worker files that might have been generated
RUN find /app/publish/wwwroot -name "*service-worker*" -type f -delete || true

# Clean up any service worker files that might have been generated

# Final stage - nginx pour servir les fichiers statiques
FROM nginx:alpine AS final
WORKDIR /usr/share/nginx/html

# Copier les fichiers publiés
COPY --from=publish /app/publish/wwwroot .

# Copier la configuration nginx
COPY src/Web/AdminPortal/nginx.conf /etc/nginx/nginx.conf

# Remove any service worker files
RUN find /usr/share/nginx/html -name "*service-worker*" -type f -delete || true
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
