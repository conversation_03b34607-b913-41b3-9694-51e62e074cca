# Test simple de persistance du panier
Write-Host "Test de persistance du panier NafaPlace" -ForegroundColor Green

# Configuration
$cartApiUrl = "http://localhost:5003"
$identityApiUrl = "http://localhost:5155"

# Etape 1: Creer un ID invite et ajouter des produits au panier
Write-Host "Etape 1: Simulation d'un utilisateur invite" -ForegroundColor Yellow

$guestId = "guest_" + [System.Guid]::NewGuid().ToString("N")
Write-Host "ID invite genere: $guestId"

# Ajouter un produit au panier invite
$addToCartRequest = @{
    ProductId = 1
    ProductName = "Test Product"
    Price = 50000
    Quantity = 2
    VariantId = $null
    VariantName = $null
} | ConvertTo-Json

try {
    Write-Host "Ajout d'un produit au panier invite..."
    $response = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId/items" -Method POST -Body $addToCartRequest -ContentType "application/json"
    Write-Host "SUCCESS: Produit ajoute au panier invite" -ForegroundColor Green
    Write-Host "Nombre d'articles dans le panier: $($response.ItemCount)"
} catch {
    Write-Host "ERROR: Erreur lors de l'ajout au panier invite: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Etape 2: Verifier le contenu du panier invite
Write-Host "Etape 2: Verification du panier invite" -ForegroundColor Yellow

try {
    $guestCart = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId" -Method GET
    Write-Host "SUCCESS: Panier invite recupere" -ForegroundColor Green
    Write-Host "Nombre d'articles: $($guestCart.ItemCount)"
    Write-Host "Total: $($guestCart.Total) GNF"
} catch {
    Write-Host "ERROR: Erreur lors de la recuperation du panier invite: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Etape 3: Connexion utilisateur
Write-Host "Etape 3: Connexion utilisateur" -ForegroundColor Yellow

$loginRequest = @{
    Email = "<EMAIL>"
    Password = "User123!"
} | ConvertTo-Json

try {
    Write-Host "Connexion de l'utilisateur..."
    $loginResponse = Invoke-RestMethod -Uri "$identityApiUrl/api/auth/login" -Method POST -Body $loginRequest -ContentType "application/json"
    Write-Host "SUCCESS: Connexion reussie" -ForegroundColor Green
    $userId = $loginResponse.user.id
    Write-Host "ID utilisateur: $userId"
} catch {
    Write-Host "ERROR: Erreur lors de la connexion: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Etape 4: Verification du panier utilisateur
Write-Host "Etape 4: Verification du panier utilisateur" -ForegroundColor Yellow

try {
    $userCart = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId" -Method GET
    Write-Host "SUCCESS: Panier utilisateur recupere" -ForegroundColor Green
    Write-Host "Nombre d'articles utilisateur: $($userCart.ItemCount)"
    Write-Host "Total utilisateur: $($userCart.Total) GNF"
} catch {
    Write-Host "INFO: Panier utilisateur vide ou inexistant"
    $userCart = @{ ItemCount = 0; Total = 0 }
}

# Nettoyage
Write-Host "Nettoyage..." -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId" -Method DELETE
    Write-Host "SUCCESS: Panier invite nettoye"
} catch {
    Write-Host "WARNING: Erreur lors du nettoyage du panier invite"
}

Write-Host "Test termine!" -ForegroundColor Green
