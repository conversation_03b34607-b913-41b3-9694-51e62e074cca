# Guide de Déploiement NafaPlace sur Fly.io

## 🚀 Déploiement de Test

Ce guide vous explique comment déployer NafaPlace en environnement de test sur fly.io avec Cloudinary pour la gestion des images.

### Prérequis

1. **Compte Fly.io** - Créez un compte sur [fly.io](https://fly.io)
2. **Fly CLI** - Installez depuis [fly.io/docs/getting-started/installing-flyctl/](https://fly.io/docs/getting-started/installing-flyctl/)
3. **Compte Cloudinary** - Créez un compte sur [cloudinary.com](https://cloudinary.com)
4. **Compte Stripe** - Pour les paiements (mode test)

### Variables d'Environnement Requises

Configurez ces variables dans votre environnement ou dans GitHub Secrets :

```bash
# Cloudinary
CLOUDINARY_CLOUD_NAME=votre_cloud_name
CLOUDINARY_API_KEY=votre_api_key
CLOUDINARY_API_SECRET=votre_api_secret

# Stripe (clés de test)
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...

# Fly.io
FLY_API_TOKEN=votre_token_fly
```

### Déploiement Automatique

#### Option 1: Via GitHub Actions (Recommandé)

1. Configurez les secrets dans votre repository GitHub
2. Poussez votre code sur la branche `main`
3. Le workflow `.github/workflows/deploy-test.yml` se déclenche automatiquement

#### Option 2: Déploiement Manuel

1. **Authentification Fly.io**
   ```bash
   flyctl auth login
   ```

2. **Configuration des variables d'environnement**
   ```bash
   export CLOUDINARY_CLOUD_NAME="votre_cloud_name"
   export CLOUDINARY_API_KEY="votre_api_key"
   export CLOUDINARY_API_SECRET="votre_api_secret"
   export STRIPE_PUBLISHABLE_KEY="pk_test_..."
   export STRIPE_SECRET_KEY="sk_test_..."
   ```

3. **Exécution du script de déploiement**
   ```bash
   # Sur Linux/Mac
   ./scripts/deploy-complete.sh
   
   # Sur Windows (PowerShell)
   bash scripts/deploy-complete.sh
   ```

### Vérification du Déploiement

Après le déploiement, vérifiez que tout fonctionne :

1. **URL de l'application** : https://nafaplace-test.fly.dev
2. **Endpoint de santé** : https://nafaplace-test.fly.dev/health
3. **Logs en temps réel** : `flyctl logs --app nafaplace-test -f`

### Commandes Utiles

```bash
# Voir le statut de l'application
flyctl status --app nafaplace-test

# Voir les logs
flyctl logs --app nafaplace-test

# Redémarrer l'application
flyctl restart --app nafaplace-test

# Accéder au shell de l'application
flyctl ssh console --app nafaplace-test

# Mettre à jour les secrets
flyctl secrets set NOUVELLE_VARIABLE=valeur --app nafaplace-test

# Voir les secrets configurés
flyctl secrets list --app nafaplace-test
```

### Architecture de Déploiement

```
┌─────────────────────────────────────────┐
│              Fly.io                     │
├─────────────────────────────────────────┤
│  ┌─────────────────────────────────┐    │
│  │     NafaPlace Web App           │    │
│  │     (nafaplace-test)            │    │
│  │                                 │    │
│  │  - Application Web Principale   │    │
│  │  - APIs intégrées               │    │
│  │  - Port 8080                    │    │
│  └─────────────────────────────────┘    │
│                                         │
│  ┌─────────────────────────────────┐    │
│  │     PostgreSQL Database         │    │
│  │     (nafaplace-test-db)         │    │
│  └─────────────────────────────────┘    │
└─────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────┐
│            Services Externes            │
├─────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ Cloudinary  │  │     Stripe      │   │
│  │   Images    │  │   Paiements     │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

### Dépannage

#### Problèmes Courants

1. **Application ne démarre pas**
   ```bash
   flyctl logs --app nafaplace-test
   ```

2. **Base de données non accessible**
   ```bash
   flyctl postgres connect --app nafaplace-test-db
   ```

3. **Images ne s'affichent pas**
   - Vérifiez les variables Cloudinary
   - Consultez les logs pour les erreurs d'upload

4. **Erreurs de paiement**
   - Vérifiez les clés Stripe (mode test)
   - Consultez les webhooks Stripe

#### Logs et Monitoring

```bash
# Logs en temps réel
flyctl logs --app nafaplace-test -f

# Métriques de performance
flyctl metrics --app nafaplace-test

# État détaillé
flyctl status --app nafaplace-test --all
```

### Mise à Jour

Pour mettre à jour l'application :

```bash
# Redéploiement
flyctl deploy --app nafaplace-test

# Ou via GitHub Actions (push sur main)
git push origin main
```

### Environnements

- **Test** : nafaplace-test.fly.dev (environnement actuel)
- **Production** : À configurer ultérieurement avec `nafaplace-prod`

### Support

En cas de problème :
1. Consultez les logs : `flyctl logs --app nafaplace-test`
2. Vérifiez le statut : `flyctl status --app nafaplace-test`
3. Testez l'endpoint de santé : https://nafaplace-test.fly.dev/health

---

🎉 **Votre application NafaPlace est maintenant déployée en test sur fly.io !**
