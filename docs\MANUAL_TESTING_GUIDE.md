# 🧪 Guide de Test Manuel - Nouvelles Fonctionnalités NafaPlace

## 📋 Checklist de Test

### ✅ **1. Système de Coupons et Promotions**

#### Test 1.1 : Application de Coupon dans le Panier
1. **Aller sur** : http://localhost:8080
2. **Ajouter des produits** au panier (valeur > 50,000 GNF)
3. **Aller au panier** : http://localhost:8080/cart
4. **Entrer le code** : `WELCOME10`
5. **Cliquer** "Appliquer le coupon"
6. **Vérifier** : Réduction de 10% appliquée

#### Test 1.2 : Coupon Livraison Gratuite
1. **Ajouter des produits** (valeur > 100,000 GNF)
2. **Entrer le code** : `FREESHIP`
3. **Vérifier** : Frais de livraison = 0 GNF

#### Test 1.3 : Coupon Montant Fixe
1. **Ajouter des produits** (valeur > 200,000 GNF)
2. **Entrer le code** : `SAVE50K`
3. **Vérifier** : Réduction de 50,000 GNF

#### Test 1.4 : Gestion Admin des Coupons
1. **Aller sur** : http://localhost:8081/admin/coupons
2. **Créer un nouveau coupon**
3. **Modifier un coupon existant**
4. **Désactiver/Activer un coupon**

---

### ✅ **2. Gestion Avancée des Stocks**

#### Test 2.1 : Réservation Automatique
1. **Ajouter un produit** au panier
2. **Vérifier** : Stock réservé pendant 15 minutes
3. **Attendre l'expiration** ou **finaliser la commande**
4. **Vérifier** : Stock libéré ou confirmé

#### Test 2.2 : Alertes de Stock (Vendeur)
1. **Aller sur** : http://localhost:8082/seller/inventory
2. **Réduire le stock** d'un produit à < 10
3. **Vérifier** : Alerte de stock faible générée
4. **Réduire à 0** : Alerte de rupture

#### Test 2.3 : Historique des Mouvements
1. **Aller sur** : http://localhost:8082/seller/inventory/movements
2. **Vérifier** : Tous les mouvements enregistrés
3. **Filtrer par produit** et **par date**

#### Test 2.4 : Dashboard Inventaire
1. **Aller sur** : http://localhost:8082/seller/dashboard
2. **Vérifier** : Statistiques de stock
3. **Voir** : Produits en stock faible
4. **Voir** : Alertes actives

---

### ✅ **3. Notifications Email et SMS**

#### Test 3.1 : Notification de Bienvenue
1. **Créer un nouveau compte** sur http://localhost:8080/register
2. **Vérifier** : Email de bienvenue reçu
3. **Vérifier** : Template utilisé correctement

#### Test 3.2 : Notification de Commande
1. **Passer une commande** complète
2. **Vérifier** : Email de confirmation reçu
3. **Vérifier** : SMS de confirmation (si configuré)

#### Test 3.3 : Gestion des Templates (Admin)
1. **Aller sur** : http://localhost:8081/admin/notifications/templates
2. **Modifier un template** existant
3. **Créer un nouveau template**
4. **Tester l'envoi** d'une notification

#### Test 3.4 : Préférences Utilisateur
1. **Aller sur** : http://localhost:8080/profile/notifications
2. **Modifier les préférences** de notification
3. **Désactiver certains types** de notifications
4. **Configurer les heures de silence**

---

### ✅ **4. Système de Livraison Avancé**

#### Test 4.1 : Calcul des Frais de Livraison
1. **Aller au checkout** : http://localhost:8080/checkout
2. **Entrer différentes adresses** :
   - Conakry Centre → ~15,000 GNF
   - Kankan → ~50,000 GNF
   - Autres localités → ~60,000 GNF
3. **Vérifier** : Calcul automatique correct

#### Test 4.2 : Options de Livraison
1. **Sélectionner Conakry** comme zone
2. **Vérifier** : Options disponibles
   - Standard (1-2 jours)
   - Express (+20,000 GNF)
   - Même jour (+35,000 GNF)

#### Test 4.3 : Gestion des Zones (Admin)
1. **Aller sur** : http://localhost:8081/admin/delivery/zones
2. **Modifier une zone** existante
3. **Créer une nouvelle zone**
4. **Configurer les tarifs**

#### Test 4.4 : Gestion des Transporteurs
1. **Aller sur** : http://localhost:8081/admin/delivery/carriers
2. **Voir les transporteurs** disponibles
3. **Modifier les configurations**
4. **Assigner zones aux transporteurs**

---

## 🔍 **Tests d'Intégration**

### Scénario Complet : Commande avec Toutes les Fonctionnalités

1. **Créer un compte** → Notification de bienvenue
2. **Ajouter des produits** → Réservation de stock
3. **Appliquer un coupon** → Réduction calculée
4. **Choisir la livraison** → Frais calculés selon zone
5. **Finaliser la commande** → Notifications envoyées
6. **Suivre la commande** → Mises à jour de statut

---

## 📊 **Vérifications en Base de Données**

### Requêtes SQL de Vérification

```sql
-- Vérifier les coupons
SELECT * FROM "Coupons" WHERE "IsActive" = true;

-- Vérifier les réservations de stock
SELECT * FROM "StockReservations" WHERE "Status" = 1;

-- Vérifier les notifications envoyées
SELECT * FROM "NotificationLogs" ORDER BY "CreatedAt" DESC LIMIT 10;

-- Vérifier les zones de livraison
SELECT * FROM "DeliveryZones" WHERE "IsActive" = true;

-- Vérifier les transporteurs
SELECT * FROM "Carriers" WHERE "IsActive" = true;
```

---

## 🚨 **Points d'Attention**

### Erreurs Communes à Vérifier

1. **Coupons** :
   - ❌ Code non reconnu
   - ❌ Conditions non respectées
   - ❌ Limite d'usage dépassée

2. **Stocks** :
   - ❌ Réservation non libérée
   - ❌ Alertes non générées
   - ❌ Stock négatif

3. **Notifications** :
   - ❌ Emails non envoyés
   - ❌ Templates mal formatés
   - ❌ Variables non remplacées

4. **Livraison** :
   - ❌ Frais mal calculés
   - ❌ Zones non reconnues
   - ❌ Options indisponibles

---

## 📝 **Rapport de Test**

### Template de Rapport

```
Date: ___________
Testeur: ___________

✅ Coupons: ___/4 tests réussis
✅ Stocks: ___/4 tests réussis  
✅ Notifications: ___/4 tests réussis
✅ Livraison: ___/4 tests réussis

Total: ___/16 tests réussis (___%)

Problèmes identifiés:
- 
- 
- 

Actions correctives:
- 
- 
- 
```

---

## 🎯 **Critères de Succès**

- **85%+ des tests** passent avec succès
- **Aucune erreur critique** dans les logs
- **Performance acceptable** (< 3s par page)
- **Données cohérentes** en base
- **Expérience utilisateur fluide**

---

**Bonne chance pour les tests ! 🚀**
