using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Infrastructure.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.OpenApi.Models;
using System.Reflection;
using System.IO;
using Microsoft.Extensions.Logging;
using NafaPlace.Identity.Infrastructure.Data;
using NafaPlace.Identity.Infrastructure;
using NafaPlace.Identity.API.Data;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddInfrastructure(builder.Configuration);

// Configuration de l'authentification JWT
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["JwtSettings:Issuer"],
        ValidAudience = builder.Configuration["JwtSettings:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["JwtSettings:Secret"] ?? throw new InvalidOperationException("JWT Secret not configured"))),
        ClockSkew = TimeSpan.Zero
    };

    // Ajouter des logs détaillés pour le débogage de l'authentification
    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            Console.WriteLine($"Échec d'authentification: {context.Exception.Message}");
            return Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            Console.WriteLine("Token validé avec succès");
            return Task.CompletedTask;
        },
        OnMessageReceived = context =>
        {
            Console.WriteLine($"Token reçu: {context.Token?.Substring(0, Math.Min(20, context.Token?.Length ?? 0))}...");
            return Task.CompletedTask;
        },
        OnChallenge = context =>
        {
            Console.WriteLine($"Challenge d'authentification: {context.Error}, {context.ErrorDescription}");
            return Task.CompletedTask;
        }
    };
});

// Configuration HttpClient pour communiquer avec le service Catalog
builder.Services.AddHttpClient<IAuthService, AuthService>(client =>
{
    // URL du service Catalog (sera configurée via Docker Compose)
    client.BaseAddress = new Uri("http://nafaplace-catalog-api:8080/");
    client.DefaultRequestHeaders.Add("Accept", "application/json");
});

// Enregistrement des services
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<NafaPlace.Identity.API.Services.IUserRoleFixService, NafaPlace.Identity.API.Services.UserRoleFixService>();

// Ajout des services d'application

// Ajouter CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.WithOrigins(
                "http://localhost:8080",   // Client Portal
                "http://localhost:8081",   // Admin Portal
                "http://localhost:8082",   // Seller Portal
                "https://nafaplace-web-test.fly.dev",     // Production Client Portal
                "https://nafaplace-admin-test.fly.dev",   // Production Admin Portal
                "https://nafaplace-seller-test.fly.dev"   // Production Seller Portal
            )
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
            .WithExposedHeaders("Authorization");
    });
});

// Configurer l'autorisation pour permettre l'accès anonyme par défaut
builder.Services.AddAuthorization(options =>
{
    // Ne pas modifier la politique par défaut pour conserver le comportement standard d'autorisation
    // Cela garantit que les attributs [Authorize] fonctionnent correctement
});

builder.Services.AddControllers();

// Configuration de Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "NafaPlace Identity API",
        Version = "v1",
        Description = "API d'authentification et de gestion des utilisateurs pour la plateforme NafaPlace",
        Contact = new OpenApiContact
        {
            Name = "NafaPlace Team",
            Email = "<EMAIL>"
        }
    });

    // Configuration de l'authentification JWT dans Swagger
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    options.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Commenté pour éviter l'erreur de fichier XML manquant
    // var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    // var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    // options.IncludeXmlComments(xmlPath);

    options.EnableAnnotations();
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "NafaPlace Identity API V1");
        c.RoutePrefix = string.Empty; // Pour servir la documentation à la racine
    });
}

app.UseHttpsRedirection();

app.UseCors("AllowAll");

// Ajout du middleware d'authentification
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Appliquer les migrations automatiquement en développement
try
{
    using (var scope = app.Services.CreateScope())
    {
        var dbContext = scope.ServiceProvider.GetRequiredService<NafaPlace.Identity.Infrastructure.Data.IdentityDbContext>();
        // Vérifier si la base de données existe, sinon la créer
        
        if (dbContext.Database.IsRelational())
        {
            // Vérifier si des migrations sont nécessaires
            var pendingMigrations = dbContext.Database.GetPendingMigrations().ToList();
            if (pendingMigrations.Any())
            {
                Console.WriteLine($"Application de {pendingMigrations.Count} migrations en attente...");
                dbContext.Database.Migrate();
            }
            else
            {
                Console.WriteLine("Aucune migration en attente.");
            }
        }
        
        // Initialiser les données de test si nécessaire
        await SeedData.Initialize(app.Services);
    }
}
catch (Exception ex)
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();
    logger.LogError(ex, "Une erreur s'est produite lors de la migration ou de l'initialisation de la base de données.");
}

app.Run();
