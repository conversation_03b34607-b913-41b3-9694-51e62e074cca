# Test de fusion du panier invite avec le panier utilisateur
Write-Host "Test de fusion du panier NafaPlace" -ForegroundColor Green

# Configuration
$cartApiUrl = "http://localhost:5003"
$identityApiUrl = "http://localhost:5155"

# Etape 1: Creer un panier invite avec des produits
Write-Host "Etape 1: Creation du panier invite" -ForegroundColor Yellow

$guestId = "guest_" + [System.Guid]::NewGuid().ToString("N")
Write-Host "ID invite: $guestId"

# Ajouter produit 1 au panier invite
$product1 = @{
    ProductId = 1
    ProductName = "Produit Invite 1"
    Price = 25000
    Quantity = 1
    VariantId = $null
    VariantName = $null
} | ConvertTo-Json

try {
    $response1 = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId/items" -Method POST -Body $product1 -ContentType "application/json"
    Write-Host "SUCCESS: Produit 1 ajoute au panier invite" -ForegroundColor Green
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Ajouter produit 2 au panier invite
$product2 = @{
    ProductId = 2
    ProductName = "Produit Invite 2"
    Price = 35000
    Quantity = 2
    VariantId = $null
    VariantName = $null
} | ConvertTo-Json

try {
    $response2 = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId/items" -Method POST -Body $product2 -ContentType "application/json"
    Write-Host "SUCCESS: Produit 2 ajoute au panier invite" -ForegroundColor Green
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Verifier le panier invite
$guestCart = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId" -Method GET
Write-Host "Panier invite - Articles: $($guestCart.ItemCount), Total: $($guestCart.Total) GNF"

# Etape 2: Connexion utilisateur
Write-Host "Etape 2: Connexion utilisateur" -ForegroundColor Yellow

$loginRequest = @{
    Email = "<EMAIL>"
    Password = "User123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$identityApiUrl/api/auth/login" -Method POST -Body $loginRequest -ContentType "application/json"
    $userId = $loginResponse.user.id
    Write-Host "SUCCESS: Utilisateur connecte (ID: $userId)" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Connexion echouee: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Etape 3: Ajouter un produit au panier utilisateur (avant fusion)
Write-Host "Etape 3: Ajout produit au panier utilisateur" -ForegroundColor Yellow

$userProduct = @{
    ProductId = 3
    ProductName = "Produit Utilisateur"
    Price = 45000
    Quantity = 1
    VariantId = $null
    VariantName = $null
} | ConvertTo-Json

try {
    $userResponse = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId/items" -Method POST -Body $userProduct -ContentType "application/json"
    Write-Host "SUCCESS: Produit ajoute au panier utilisateur" -ForegroundColor Green
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Verifier le panier utilisateur avant fusion
$userCartBefore = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId" -Method GET
Write-Host "Panier utilisateur AVANT fusion - Articles: $($userCartBefore.ItemCount), Total: $($userCartBefore.Total) GNF"

# Etape 4: Simulation de la fusion manuelle
Write-Host "Etape 4: Fusion des paniers" -ForegroundColor Yellow

if ($guestCart.Items -and $guestCart.Items.Count -gt 0) {
    Write-Host "Fusion de $($guestCart.Items.Count) articles du panier invite..."
    
    foreach ($item in $guestCart.Items) {
        $mergeRequest = @{
            ProductId = $item.ProductId
            ProductName = $item.ProductName
            Price = $item.UnitPrice
            Quantity = $item.Quantity
            VariantId = $item.VariantId
            VariantName = $item.VariantName
        } | ConvertTo-Json
        
        try {
            $mergeResponse = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId/items" -Method POST -Body $mergeRequest -ContentType "application/json"
            Write-Host "SUCCESS: Article fusionne - $($item.ProductName) x$($item.Quantity)" -ForegroundColor Green
        } catch {
            Write-Host "ERROR: Fusion echouee pour $($item.ProductName): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Etape 5: Verification finale
Write-Host "Etape 5: Verification finale" -ForegroundColor Yellow

$userCartAfter = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId" -Method GET
Write-Host "Panier utilisateur APRES fusion - Articles: $($userCartAfter.ItemCount), Total: $($userCartAfter.Total) GNF"

# Analyser les resultats
$expectedItems = $userCartBefore.ItemCount + $guestCart.ItemCount
Write-Host ""
Write-Host "RESULTATS:" -ForegroundColor Cyan
Write-Host "- Articles panier invite: $($guestCart.ItemCount)"
Write-Host "- Articles panier utilisateur (avant): $($userCartBefore.ItemCount)"
Write-Host "- Articles attendus apres fusion: $expectedItems"
Write-Host "- Articles obtenus apres fusion: $($userCartAfter.ItemCount)"

if ($userCartAfter.ItemCount -eq $expectedItems) {
    Write-Host "SUCCESS: La fusion du panier fonctionne parfaitement!" -ForegroundColor Green
} else {
    Write-Host "WARNING: Fusion partielle ou probleme detecte" -ForegroundColor Yellow
}

# Nettoyage
Write-Host "Nettoyage..." -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId" -Method DELETE
    Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId" -Method DELETE
    Write-Host "SUCCESS: Paniers nettoyes"
} catch {
    Write-Host "WARNING: Erreur lors du nettoyage"
}

Write-Host "Test termine!" -ForegroundColor Green
