using Microsoft.EntityFrameworkCore;
using Moq;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Application.DTOs.Auth;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Data;
using NafaPlace.Identity.Infrastructure.Services;
using System.Collections.Generic;
using Xunit;

namespace NafaPlace.Identity.UnitTests.Services;

public class AuthServiceTests
{
    private readonly Mock<IJwtService> _jwtServiceMock;
    private readonly DbContextOptions<IdentityDbContext> _dbContextOptions;

    public AuthServiceTests()
    {
        _jwtServiceMock = new Mock<IJwtService>();
        _dbContextOptions = new DbContextOptionsBuilder<IdentityDbContext>()
            .UseInMemoryDatabase(databaseName: int.Newint().ToString())
            .Options;
    }

    [Fact]
    public async Task RegisterAsync_WithValidRequest_ShouldCreateUserAndReturnAuthResponse()
    {
        // Arrange
        var request = new RegisterRequest
        {
            Email = "<EMAIL>",
            Username = "testuser",
            Password = "Password123!",
            FirstName = "Test",
            LastName = "User"
        };

        var accessToken = "test-access-token";
        var refreshToken = "test-refresh-token";

        _jwtServiceMock.Setup(x => x.GenerateAccessToken(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<IEnumerable<string>>()))
            .Returns(accessToken);
        _jwtServiceMock.Setup(x => x.GenerateRefreshToken())
            .Returns(refreshToken);

        await using var context = new IdentityDbContext(_dbContextOptions);
        var service = new AuthService(context, _jwtServiceMock.Object);

        // Act
        var result = await service.RegisterAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(accessToken, result.AccessToken);
        Assert.Equal(refreshToken, result.RefreshToken);
        Assert.Equal(request.Email, result.User.Email);
        Assert.Equal(request.Username, result.User.Username);

        var userInDb = await context.Users.FirstOrDefaultAsync(u => u.Email == request.Email);
        Assert.NotNull(userInDb);
        Assert.Equal(request.Username, userInDb.Username);
    }

    [Fact]
    public async Task RegisterAsync_WithExistingEmail_ShouldThrowAuthenticationException()
    {
        // Arrange
        var existingUser = new User
        {
            Email = "<EMAIL>",
            Username = "existinguser",
            PasswordHash = "hash",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(existingUser);
        await context.SaveChangesAsync();

        var request = new RegisterRequest
        {
            Email = "<EMAIL>",
            Username = "newuser",
            Password = "Password123!",
            FirstName = "New",
            LastName = "User"
        };

        var service = new AuthService(context, _jwtServiceMock.Object);

        // Act & Assert
        await Assert.ThrowsAsync<AuthenticationException>(() => service.RegisterAsync(request));
    }

    [Fact]
    public async Task LoginAsync_WithValidCredentials_ShouldReturnAuthResponse()
    {
        // Arrange
        var user = new User
        {
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("Password123!"),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        await context.SaveChangesAsync();

        var request = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "Password123!"
        };

        var accessToken = "test-access-token";
        var refreshToken = "test-refresh-token";

        _jwtServiceMock.Setup(x => x.GenerateAccessToken(It.IsAny<User>(), It.IsAny<IEnumerable<string>>()))
            .Returns(accessToken);
        _jwtServiceMock.Setup(x => x.GenerateRefreshToken())
            .Returns(refreshToken);

        var service = new AuthService(context, _jwtServiceMock.Object);

        // Act
        var result = await service.LoginAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(accessToken, result.AccessToken);
        Assert.Equal(refreshToken, result.RefreshToken);
        Assert.Equal(user.Email, result.User.Email);
    }

    [Fact]
    public async Task LoginAsync_WithInvalidCredentials_ShouldThrowAuthenticationException()
    {
        // Arrange
        var user = new User
        {
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("Password123!"),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User"
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        await context.SaveChangesAsync();

        var request = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "WrongPassword!"
        };

        var service = new AuthService(context, _jwtServiceMock.Object);

        // Act & Assert
        await Assert.ThrowsAsync<AuthenticationException>(() => service.LoginAsync(request));
    }

    [Fact]
    public async Task RefreshTokenAsync_WithValidToken_ShouldReturnNewAuthResponse()
    {
        // Arrange
        var user = new User
        {
            Email = "<EMAIL>",
            Username = "testuser",
            PasswordHash = "hash",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsActive = true,
            Roles = "User"
        };

        var refreshToken = new RefreshToken
        {
            Token = "valid-refresh-token",
            UserId = user.Id,
            ExpiryDate = DateTime.UtcNow.AddDays(7),
            CreatedAt = DateTime.UtcNow,
            IsRevoked = false,
            User = user
        };

        await using var context = new IdentityDbContext(_dbContextOptions);
        context.Users.Add(user);
        context.RefreshTokens.Add(refreshToken);
        await context.SaveChangesAsync();

        var request = new RefreshTokenRequest
        {
            RefreshToken = "valid-refresh-token"
        };

        var newAccessToken = "new-access-token";
        var newRefreshToken = "new-refresh-token";

        _jwtServiceMock.Setup(x => x.GenerateAccessToken(It.IsAny<User>(), It.IsAny<IEnumerable<string>>()))
            .Returns(newAccessToken);
        _jwtServiceMock.Setup(x => x.GenerateRefreshToken())
            .Returns(newRefreshToken);

        var service = new AuthService(context, _jwtServiceMock.Object);

        // Act
        var result = await service.RefreshTokenAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(newAccessToken, result.AccessToken);
        Assert.Equal(newRefreshToken, result.RefreshToken);
        Assert.Equal(user.Email, result.User.Email);

        // Verify old token is revoked
        var oldToken = await context.RefreshTokens.FirstAsync(rt => rt.Token == refreshToken.Token);
        Assert.True(oldToken.IsRevoked);
    }
}
