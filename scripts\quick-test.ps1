# Script PowerShell pour un test rapide des services NafaPlace
# Exécuter après le démarrage de Docker

Write-Host "🚀 Test Rapide des Services NafaPlace" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Configuration des URLs
$services = @(
    @{ Name = "Main Web Portal"; Url = "http://localhost:8080"; Port = 8080 },
    @{ Name = "Admin Portal"; Url = "http://localhost:8081"; Port = 8081 },
    @{ Name = "Seller Portal"; Url = "http://localhost:8082"; Port = 8082 },
    @{ Name = "PostgreSQL"; Url = "localhost:5432"; Port = 5432 }
)

# Fonction pour tester un port
function Test-Port {
    param($ComputerName, $Port)
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.ConnectAsync($ComputerName, $Port).Wait(3000)
        $result = $tcpClient.Connected
        $tcpClient.Close()
        return $result
    } catch {
        return $false
    }
}

# Fonction pour tester une URL HTTP
function Test-HttpUrl {
    param($Url)
    try {
        $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 5 -UseBasicParsing
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

Write-Host "`n🔍 Test de Connectivité des Ports..." -ForegroundColor Cyan

foreach ($service in $services) {
    $portOpen = Test-Port "localhost" $service.Port
    if ($portOpen) {
        Write-Host "✅ $($service.Name) - Port $($service.Port) ouvert" -ForegroundColor Green
    } else {
        Write-Host "❌ $($service.Name) - Port $($service.Port) fermé" -ForegroundColor Red
    }
}

Write-Host "`n🌐 Test de Connectivité HTTP..." -ForegroundColor Cyan

foreach ($service in $services[0..2]) {  # Seulement les services web
    $httpOk = Test-HttpUrl $service.Url
    if ($httpOk) {
        Write-Host "✅ $($service.Name) - HTTP OK" -ForegroundColor Green
    } else {
        Write-Host "❌ $($service.Name) - HTTP KO" -ForegroundColor Red
    }
}

Write-Host "`n🐳 État des Conteneurs Docker..." -ForegroundColor Cyan

try {
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    Write-Host $containers -ForegroundColor Gray
} catch {
    Write-Host "Error: Cannot retrieve Docker containers status" -ForegroundColor Red
}

Write-Host "`n📊 Test des APIs Principales..." -ForegroundColor Cyan

# Test des endpoints principaux
$apiTests = @(
    @{ Name = "API Products"; Url = "http://localhost:8080/api/products" },
    @{ Name = "API Categories"; Url = "http://localhost:8080/api/categories" },
    @{ Name = "API Health"; Url = "http://localhost:8080/health" }
)

foreach ($api in $apiTests) {
    try {
        $response = Invoke-RestMethod -Uri $api.Url -Method GET -TimeoutSec 5
        Write-Host "✅ $($api.Name) - Réponse OK" -ForegroundColor Green
    } catch {
        Write-Host "Error $($api.Name) - Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎯 Test des Nouvelles Fonctionnalités..." -ForegroundColor Cyan

# Test spécifique des nouvelles APIs
$newFeatureTests = @(
    @{ Name = "Coupons API"; Url = "http://localhost:8080/api/coupons" },
    @{ Name = "Inventory API"; Url = "http://localhost:8080/api/inventory/dashboard" },
    @{ Name = "Notifications API"; Url = "http://localhost:8080/api/notifications/templates" },
    @{ Name = "Delivery API"; Url = "http://localhost:8080/api/delivery/zones" }
)

$newFeaturesWorking = 0
foreach ($test in $newFeatureTests) {
    try {
        $response = Invoke-RestMethod -Uri $test.Url -Method GET -TimeoutSec 5
        Write-Host "✅ $($test.Name) - Fonctionnel" -ForegroundColor Green
        $newFeaturesWorking++
    } catch {
        Write-Host "❌ $($test.Name) - Non disponible" -ForegroundColor Red
    }
}

Write-Host "`n📋 Résumé du Test..." -ForegroundColor Cyan
Write-Host "-------------------"

$totalNewFeatures = $newFeatureTests.Count
$successRate = [math]::Round(($newFeaturesWorking / $totalNewFeatures) * 100, 2)

Write-Host "🎯 Nouvelles fonctionnalités: $newFeaturesWorking/$totalNewFeatures ($successRate%)" -ForegroundColor Blue

if ($successRate -eq 100) {
    Write-Host "`n🎉 PARFAIT ! Toutes les nouvelles fonctionnalités sont opérationnelles !" -ForegroundColor Green
    Write-Host "Vous pouvez maintenant procéder aux tests manuels détaillés." -ForegroundColor Green
} elseif ($successRate -ge 75) {
    Write-Host "`n✅ BIEN ! La plupart des fonctionnalités sont opérationnelles." -ForegroundColor Green
    Write-Host "Quelques ajustements peuvent être nécessaires." -ForegroundColor Yellow
} elseif ($successRate -ge 50) {
    Write-Host "`n⚠️ MOYEN ! Certaines fonctionnalités nécessitent des corrections." -ForegroundColor Yellow
} else {
    Write-Host "`n❌ PROBLÈME ! Des corrections importantes sont nécessaires." -ForegroundColor Red
}

Write-Host "`n🔗 URLs de Test Rapide:" -ForegroundColor Cyan
Write-Host "Main Web: http://localhost:8080"
Write-Host "Admin Portal: http://localhost:8081"
Write-Host "Seller Portal: http://localhost:8082"

Write-Host "`n📝 Actions Recommandées:" -ForegroundColor Cyan
if ($successRate -lt 100) {
    Write-Host "1. Vérifier les logs Docker: docker compose logs"
    Write-Host "2. Redémarrer les services: docker compose restart"
    Write-Host "3. Exécuter la migration: .\scripts\run-migration.ps1"
}
Write-Host "4. Test manuel complet: docs\MANUAL_TESTING_GUIDE.md"
Write-Host "5. Test automatisé: .\scripts\test-new-features.ps1"

Write-Host "`nTest rapide terminé ! 🏁" -ForegroundColor Green
