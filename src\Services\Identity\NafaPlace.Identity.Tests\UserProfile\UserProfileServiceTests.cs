using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Moq;
using NafaPlace.Identity.Application.DTOs;
using NafaPlace.Identity.Application.Services;
using NafaPlace.Identity.Domain.Models;
using Xunit;

namespace NafaPlace.Identity.Tests.UserProfile
{
    public class UserProfileServiceTests
    {
        private readonly Mock<UserManager<ApplicationUser>> _userManagerMock;
        private readonly UserProfileService _userProfileService;

        public UserProfileServiceTests()
        {
            _userManagerMock = new Mock<UserManager<ApplicationUser>>();
            _userProfileService = new UserProfileService(_userManagerMock.Object);
        }

        [Fact]
        public async Task GetUserProfile_WithValidId_ReturnsUserProfile()
        {
            // Arrange
            var userId = "user123";
            var user = new ApplicationUser
            {
                Id = userId,
                Email = "<EMAIL>",
                UserName = "testuser",
                FirstName = "Test",
                LastName = "User",
                PhoneNumber = "1234567890",
                Address = "123 Test Street"
            };

            _userManagerMock.Setup(x => x.FindByIdAsync(userId))
                .ReturnsAsync(user);

            // Act
            var result = await _userProfileService.GetUserProfileAsync(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(user.Email, result.Email);
            Assert.Equal(user.FirstName, result.FirstName);
            Assert.Equal(user.LastName, result.LastName);
            Assert.Equal(user.PhoneNumber, result.PhoneNumber);
            Assert.Equal(user.Address, result.Address);
        }

        [Fact]
        public async Task UpdateUserProfile_WithValidData_ReturnsSuccess()
        {
            // Arrange
            var userId = "user123";
            var user = new ApplicationUser
            {
                Id = userId,
                Email = "<EMAIL>",
                UserName = "testuser"
            };

            var updateProfileDto = new UpdateProfileDto
            {
                FirstName = "Updated",
                LastName = "User",
                PhoneNumber = "0987654321",
                Address = "456 New Street"
            };

            _userManagerMock.Setup(x => x.FindByIdAsync(userId))
                .ReturnsAsync(user);

            _userManagerMock.Setup(x => x.UpdateAsync(It.IsAny<ApplicationUser>()))
                .ReturnsAsync(IdentityResult.Success);

            // Act
            var result = await _userProfileService.UpdateUserProfileAsync(userId, updateProfileDto);

            // Assert
            Assert.True(result.Succeeded);
            _userManagerMock.Verify(x => x.UpdateAsync(It.Is<ApplicationUser>(u => 
                u.FirstName == updateProfileDto.FirstName &&
                u.LastName == updateProfileDto.LastName &&
                u.PhoneNumber == updateProfileDto.PhoneNumber &&
                u.Address == updateProfileDto.Address
            )), Times.Once);
        }

        [Fact]
        public async Task ChangePassword_WithValidData_ReturnsSuccess()
        {
            // Arrange
            var userId = "user123";
            var user = new ApplicationUser
            {
                Id = userId,
                Email = "<EMAIL>"
            };

            var changePasswordDto = new ChangePasswordDto
            {
                CurrentPassword = "OldPassword123!",
                NewPassword = "NewPassword123!"
            };

            _userManagerMock.Setup(x => x.FindByIdAsync(userId))
                .ReturnsAsync(user);

            _userManagerMock.Setup(x => x.ChangePasswordAsync(user, changePasswordDto.CurrentPassword, changePasswordDto.NewPassword))
                .ReturnsAsync(IdentityResult.Success);

            // Act
            var result = await _userProfileService.ChangePasswordAsync(userId, changePasswordDto);

            // Assert
            Assert.True(result.Succeeded);
        }

        [Fact]
        public async Task DeleteUserProfile_WithValidId_ReturnsSuccess()
        {
            // Arrange
            var userId = "user123";
            var user = new ApplicationUser
            {
                Id = userId,
                Email = "<EMAIL>"
            };

            _userManagerMock.Setup(x => x.FindByIdAsync(userId))
                .ReturnsAsync(user);

            _userManagerMock.Setup(x => x.DeleteAsync(user))
                .ReturnsAsync(IdentityResult.Success);

            // Act
            var result = await _userProfileService.DeleteUserProfileAsync(userId);

            // Assert
            Assert.True(result.Succeeded);
        }
    }
}
