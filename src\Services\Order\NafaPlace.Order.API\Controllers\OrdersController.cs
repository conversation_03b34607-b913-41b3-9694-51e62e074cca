using Microsoft.AspNetCore.Mvc;
using NafaPlace.Order.Application;
using NafaPlace.Order.API.Services;
using NafaPlace.Order.API.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace NafaPlace.Order.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class OrdersController : ControllerBase
    {
        private readonly IOrderRepository _repository;
        private readonly ICartService _cartService;

        public OrdersController(IOrderRepository repository, ICartService cartService)
        {
            _repository = repository;
            _cartService = cartService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Domain.Order>>> GetAllOrders(
            [FromQuery] string? searchTerm = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentStatus = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var orders = await _repository.GetOrdersWithFiltersAsync(
                searchTerm, status, paymentStatus, startDate, endDate, pageNumber, pageSize);
            return Ok(orders);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Domain.Order>> GetOrder(int id)
        {
            var order = await _repository.GetOrderByIdAsync(id);
            if (order == null)
            {
                return NotFound();
            }
            return Ok(order);
        }

        [HttpGet("user/{userId}")]
        public async Task<ActionResult<Domain.Order>> GetOrdersByUser(string userId)
        {
            var orders = await _repository.GetOrdersByUserIdAsync(userId);
            return Ok(orders);
        }

        [HttpPost]
        public async Task<ActionResult<Domain.Order>> CreateOrder(Domain.Order order)
        {
            var createdOrder = await _repository.CreateOrderAsync(order);
            return CreatedAtAction(nameof(GetOrder), new { id = createdOrder.Id }, createdOrder);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateOrder(int id, Domain.Order order)
        {
            if (id != order.Id)
            {
                return BadRequest();
            }

            await _repository.UpdateOrderAsync(order);

            return NoContent();
        }

        [HttpPost("checkout/{userId}")]
        public async Task<ActionResult<Domain.Order>> Checkout(string userId, [FromBody] CheckoutRequest request)
        {
            var cart = await _cartService.GetCartAsync(userId);
            if (cart == null || cart.Items.Count == 0)
            {
                return BadRequest("Cart is empty");
            }

            var order = new Domain.Order
            {
                UserId = userId,
                OrderDate = DateTime.UtcNow,
                TotalAmount = cart.Items.Sum(i => i.UnitPrice * i.Quantity),
                Currency = "GNF", // Franc Guinéen
                Status = Domain.OrderStatus.Pending,
                PaymentMethod = request.PaymentMethod,
                PaymentStatus = Domain.PaymentStatus.Pending,
                ShippingAddress = request.ShippingAddress != null ? new Domain.ShippingAddress
                {
                    FullName = request.ShippingAddress.FullName,
                    Address = request.ShippingAddress.Address,
                    City = request.ShippingAddress.City,
                    Country = request.ShippingAddress.Country,
                    PostalCode = request.ShippingAddress.PostalCode,
                    PhoneNumber = request.ShippingAddress.PhoneNumber
                } : null,
                OrderItems = cart.Items.Select(i => new Domain.OrderItem
                {
                    ProductId = i.ProductId,
                    ProductName = i.ProductName,
                    UnitPrice = i.UnitPrice,
                    Quantity = i.Quantity,
                    ImageUrl = i.ImageUrl
                }).ToList()
            };

            var createdOrder = await _repository.CreateOrderAsync(order);

            // Ne pas vider le panier ici - il sera vidé après un paiement réussi
            // await _cartService.ClearCartAsync(userId);

            return CreatedAtAction(nameof(GetOrder), new { id = createdOrder.Id }, createdOrder);
        }

        [HttpPost("{orderId}/initiate-payment")]
        public async Task<ActionResult> InitiatePayment(int orderId, [FromBody] InitiatePaymentRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            if (order.PaymentStatus != Domain.PaymentStatus.Pending)
            {
                return BadRequest("Payment already processed");
            }

            try
            {
                switch (order.PaymentMethod)
                {
                    case Domain.PaymentMethod.OrangeMoney:
                        // TODO: Appeler l'API Orange Money
                        return Ok(new {
                            message = "Orange Money payment initiated",
                            paymentUrl = $"http://localhost:8080/payment/orange-money?orderId={orderId}",
                            orderId = orderId
                        });

                    case Domain.PaymentMethod.Stripe:
                        // TODO: Appeler l'API Stripe
                        return Ok(new {
                            message = "Stripe payment initiated",
                            paymentUrl = $"http://localhost:8080/payment/stripe?orderId={orderId}",
                            orderId = orderId
                        });

                    case Domain.PaymentMethod.CashOnDelivery:
                        return Ok(new {
                            message = "Cash on delivery - no payment required",
                            orderId = orderId
                        });

                    default:
                        return BadRequest("Unsupported payment method");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Error initiating payment", details = ex.Message });
            }
        }

        [HttpPost("{orderId}/update-payment-status")]
        public async Task<ActionResult> UpdatePaymentStatus(int orderId, [FromBody] UpdatePaymentStatusRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            order.PaymentStatus = request.PaymentStatus;
            order.PaymentTransactionId = request.TransactionId;

            if (request.PaymentStatus == Domain.PaymentStatus.Completed)
            {
                order.PaymentDate = DateTime.UtcNow;
                order.Status = Domain.OrderStatus.Paid;

                // Vider le panier après un paiement réussi
                try
                {
                    await _cartService.ClearCartAsync(order.UserId);
                    Console.WriteLine($"Panier vidé pour l'utilisateur {order.UserId} après paiement réussi de la commande {orderId}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Erreur lors du vidage du panier pour l'utilisateur {order.UserId}: {ex.Message}");
                    // Ne pas faire échouer la mise à jour du paiement pour un problème de panier
                }
            }

            await _repository.UpdateOrderAsync(order);

            return Ok(new { message = "Payment status updated", orderId = orderId });
        }

        [HttpPut("{orderId}/status")]
        public async Task<ActionResult<UpdateOrderStatusResponse>> UpdateOrderStatus(int orderId, [FromBody] UpdateOrderStatusRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            // Convertir le statut string en enum
            if (Enum.TryParse<Domain.OrderStatus>(request.Status, out var orderStatus))
            {
                order.Status = orderStatus;
            }
            else
            {
                return BadRequest($"Invalid order status: {request.Status}");
            }

            // Mettre à jour les champs optionnels si fournis
            if (!string.IsNullOrEmpty(request.TrackingNumber))
            {
                // Ajouter le numéro de suivi si le domaine le supporte
                // Pour l'instant, nous pouvons l'ignorer ou l'ajouter comme note
            }

            await _repository.UpdateOrderAsync(order);

            var response = new UpdateOrderStatusResponse
            {
                Id = order.Id,
                Status = order.Status.ToString(),
                Message = "Order status updated successfully"
            };

            return Ok(response);
        }
    }
}