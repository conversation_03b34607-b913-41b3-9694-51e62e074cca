using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.DTOs;

namespace NafaPlace.Reviews.Application.Services;

public interface IReviewService
{
    // Basic Review operations
    Task<ReviewDto?> GetReviewByIdAsync(int id);
    Task<ReviewsPagedResult> GetReviewsByProductIdAsync(int productId, int page = 1, int pageSize = 10);
    Task<ReviewsPagedResult> GetReviewsByUserIdAsync(string userId, int page = 1, int pageSize = 10);
    Task<ReviewDto> CreateReviewAsync(CreateReviewRequest request);
    Task<ReviewDto> UpdateReviewAsync(int id, UpdateReviewRequest request, string userId);
    Task DeleteReviewAsync(int id, string userId);
    Task<ReviewSummaryDto> GetReviewSummaryAsync(int productId);
    Task<bool> MarkReviewHelpfulAsync(int reviewId, string userId);
    Task<bool> UnmarkReviewHelpfulAsync(int reviewId, string userId);
    Task<bool> CanUserReviewProductAsync(int productId, string userId);
    
    // Search and Filter operations
    Task<ReviewsPagedResult> SearchReviewsAsync(string searchTerm, int? productId = null, int? minRating = null, int? maxRating = null, bool? isVerifiedOnly = null, int page = 1, int pageSize = 10);
    
    // Moderation operations
    Task<ReviewsPagedResult> GetReviewsByStatusAsync(ReviewStatus status, int page = 1, int pageSize = 10);
    Task<ReviewDto> UpdateReviewStatusAsync(int id, ReviewStatus status, string moderatorId, string? notes = null);
    
    // Reply operations
    Task<ReplyDto?> GetReplyByIdAsync(int id);
    Task<List<ReplyDto>> GetRepliesByReviewIdAsync(int reviewId);
    Task<ReplyDto> CreateReplyAsync(CreateReplyRequest request, string userId, string userName);
    Task<ReplyDto> UpdateReplyAsync(int id, UpdateReplyRequest request, string userId);
    Task DeleteReplyAsync(int id, string userId);
    
    // Report operations
    Task<ReviewReportDto?> GetReportByIdAsync(int id);
    Task<List<ReviewReportDto>> GetReportsByReviewIdAsync(int reviewId);
    Task<ReviewReportDto> CreateReportAsync(CreateReviewReportRequest request, string userId);
    Task<ReviewReportDto> UpdateReportStatusAsync(int id, UpdateReviewReportRequest request, string moderatorId);
    Task<List<ReviewReportDto>> GetReportsByStatusAsync(ReportStatus status, int page = 1, int pageSize = 10);
    Task<ReviewReportSummaryDto> GetReportSummaryAsync();
    Task<List<ReportedReviewDto>> GetMostReportedReviewsAsync(int count = 10);
}
