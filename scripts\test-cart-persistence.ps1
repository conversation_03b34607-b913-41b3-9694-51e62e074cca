# Script de test pour la persistance du panier
# Ce script teste le scénario suivant :
# 1. Utilisateur non connecté ajoute des produits au panier
# 2. Utilisateur se connecte
# 3. Vérifier que les produits sont toujours dans le panier

Write-Host "🧪 Test de persistance du panier NafaPlace" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost:5000"
$cartApiUrl = "http://localhost:5003"
$identityApiUrl = "http://localhost:5155"

# Étape 1: Créer un ID invité et ajouter des produits au panier
Write-Host "`n📝 Étape 1: Simulation d'un utilisateur invité" -ForegroundColor Yellow

$guestId = "guest_" + [System.Guid]::NewGuid().ToString("N")
Write-Host "ID invité généré: $guestId"

# Ajouter un produit au panier invité
$addToCartRequest = @{
    ProductId = 1
    ProductName = "Test Product"
    Price = 50000
    Quantity = 2
    VariantId = $null
    VariantName = $null
} | ConvertTo-Json

try {
    Write-Host "Ajout d'un produit au panier invité..."
    $response = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId/items" -Method POST -Body $addToCartRequest -ContentType "application/json"
    Write-Host "✅ Produit ajouté au panier invité avec succès" -ForegroundColor Green
    Write-Host "Nombre d'articles dans le panier: $($response.ItemCount)"
} catch {
    Write-Host "❌ Erreur lors de l'ajout au panier invité: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Étape 2: Vérifier le contenu du panier invité
Write-Host "`n📝 Étape 2: Vérification du panier invité" -ForegroundColor Yellow

try {
    $guestCart = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId" -Method GET
    Write-Host "✅ Panier invité récupéré avec succès" -ForegroundColor Green
    Write-Host "Nombre d'articles: $($guestCart.ItemCount)"
    Write-Host "Total: $($guestCart.Total) GNF"
} catch {
    Write-Host "❌ Erreur lors de la récupération du panier invité: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Étape 3: Connexion utilisateur
Write-Host "`n📝 Étape 3: Connexion utilisateur" -ForegroundColor Yellow

$loginRequest = @{
    Email = "<EMAIL>"
    Password = "User123!"
} | ConvertTo-Json

try {
    Write-Host "Connexion de l'utilisateur..."
    $loginResponse = Invoke-RestMethod -Uri "$identityApiUrl/api/auth/login" -Method POST -Body $loginRequest -ContentType "application/json"
    Write-Host "✅ Connexion réussie" -ForegroundColor Green
    $userId = $loginResponse.user.id
    Write-Host "ID utilisateur: $userId"
} catch {
    Write-Host "❌ Erreur lors de la connexion: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Étape 4: Simulation de la fusion des paniers
Write-Host "`n📝 Étape 4: Test de fusion des paniers" -ForegroundColor Yellow

# D'abord, vérifier le panier utilisateur avant fusion
try {
    Write-Host "Vérification du panier utilisateur avant fusion..."
    $userCartBefore = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId" -Method GET
    Write-Host "Panier utilisateur avant fusion - Articles: $($userCartBefore.ItemCount)"
} catch {
    Write-Host "Panier utilisateur vide ou inexistant avant fusion"
    $userCartBefore = @{ ItemCount = 0 }
}

# Simuler la fusion en ajoutant les articles du panier invité au panier utilisateur
if ($guestCart.Items -and $guestCart.Items.Count -gt 0) {
    Write-Host "Fusion des articles du panier invité vers le panier utilisateur..."
    
    foreach ($item in $guestCart.Items) {
        $mergeRequest = @{
            ProductId = $item.ProductId
            ProductName = $item.ProductName
            Price = $item.UnitPrice
            Quantity = $item.Quantity
            VariantId = $item.VariantId
            VariantName = $item.VariantName
        } | ConvertTo-Json
        
        try {
            $mergeResponse = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId/items" -Method POST -Body $mergeRequest -ContentType "application/json"
            Write-Host "✅ Article fusionné: $($item.ProductName) x$($item.Quantity)" -ForegroundColor Green
        } catch {
            Write-Host "❌ Erreur lors de la fusion de l'article: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Étape 5: Vérification finale
Write-Host "`n📝 Étape 5: Vérification finale" -ForegroundColor Yellow

try {
    $userCartAfter = Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId" -Method GET
    Write-Host "✅ Panier utilisateur après fusion récupéré" -ForegroundColor Green
    Write-Host "Articles avant fusion: $($userCartBefore.ItemCount)"
    Write-Host "Articles du panier invité: $($guestCart.ItemCount)"
    Write-Host "Articles après fusion: $($userCartAfter.ItemCount)"
    Write-Host "Total après fusion: $($userCartAfter.Total) GNF"
    
    # Vérifier que la fusion a fonctionné
    $expectedItems = $userCartBefore.ItemCount + $guestCart.ItemCount
    if ($userCartAfter.ItemCount -eq $expectedItems) {
        Write-Host "🎉 TEST RÉUSSI: La fusion du panier fonctionne correctement!" -ForegroundColor Green
    } else {
        Write-Host "WARNING TEST PARTIELLEMENT REUSSI: Nombre d'articles different de celui attendu" -ForegroundColor Yellow
        Write-Host "Attendu: $expectedItems, Obtenu: $($userCartAfter.ItemCount)"
    }
} catch {
    Write-Host "❌ Erreur lors de la vérification finale: $($_.Exception.Message)" -ForegroundColor Red
}

# Nettoyage
Write-Host "`n🧹 Nettoyage..." -ForegroundColor Cyan
try {
    # Vider le panier invité
    Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$guestId" -Method DELETE
    Write-Host "✅ Panier invité nettoyé"
    
    # Vider le panier utilisateur
    Invoke-RestMethod -Uri "$cartApiUrl/api/cart/$userId" -Method DELETE
    Write-Host "✅ Panier utilisateur nettoyé"
} catch {
    Write-Host "WARNING Erreur lors du nettoyage: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n✅ Test terminé!" -ForegroundColor Green
