@page "/catalog/products"
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Services
@using System.Security.Claims
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject ICartService CartService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntime

<h1>Catalogue des produits</h1>

<div class="row mb-4">
    <div class="col-md-4">
        <select class="form-select" @onchange="OnCategorySelected">
            <option value="0">Toutes les catégories</option>
            @if (_categories != null)
            {
                @foreach (var category in _categories)
                {
                    <option value="@category.Id">@category.Name</option>
                }
            }
        </select>
    </div>
    <div class="col-md-4">
        <select class="form-select" @onchange="OnSortBySelected">
            <option value="name_asc">Trier par nom (A-Z)</option>
            <option value="name_desc">Trier par nom (Z-A)</option>
            <option value="price_asc">Trier par prix (croissant)</option>
            <option value="price_desc">Trier par prix (décroissant)</option>
        </select>
    </div>
</div>

@if (_productSearchResponse == null)
{
    <p><em>Chargement...</em></p>
}
else
{
    <div class="row">
        @foreach (var product in _productSearchResponse.Products)
        {
            <div class="col-md-4 mb-4">
                <div class="card">
                    <img src="@(product.Images.Any() ? product.Images.First().Url : "/images/placeholder.png")" class="card-img-top" alt="@product.Name">
                    <div class="card-body">
                        <h5 class="card-title">@product.Name</h5>
                        <p class="card-text">@product.Price.ToString("C")</p>
                        <a href="/catalog/products/@product.Id" class="btn btn-primary">Voir les détails</a>
                        <button class="btn btn-success" @onclick="() => AddToCart(product.Id)">Ajouter au panier</button>
                    </div>
                </div>
            </div>
        }
    </div>

    <nav aria-label="Page navigation">
        <ul class="pagination">
            @for (int i = 1; i <= _productSearchResponse.TotalPages; i++)
            {
                var pageNumber = i;
                <li class="page-item @(pageNumber == _productSearchResponse.Page ? "active" : "")">
                    <a class="page-link" href="#" @onclick="() => GoToPage(pageNumber)" @onclick:preventDefault>@pageNumber</a>
                </li>
            }
        </ul>
    </nav>
}

@code {
    private ProductSearchResponse? _productSearchResponse;
    private IEnumerable<CategoryDto> _categories = new List<CategoryDto>();
    private ProductSearchRequest _searchRequest = new ProductSearchRequest();
    private string _userId = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
        }

        _categories = await CategoryService.GetAllCategoriesAsync();
        await LoadProducts();
    }

    private async Task LoadProducts()
    {
        _productSearchResponse = await ProductService.SearchProductsAsync(_searchRequest);
    }

    private async Task OnCategorySelected(ChangeEventArgs e)
    {
        var categoryId = int.Parse(e.Value.ToString());
        if (categoryId == 0)
        {
            _searchRequest.CategoryIds = null;
        }
        else
        {
            _searchRequest.CategoryIds = new List<int> { categoryId };
        }
        _searchRequest.Page = 1;
        await LoadProducts();
    }

    private async Task OnSortBySelected(ChangeEventArgs e)
    {
        var sortBy = e.Value.ToString();
        _searchRequest.SortBy = sortBy.Split('_')[0];
        _searchRequest.SortDescending = sortBy.EndsWith("_desc");
        _searchRequest.Page = 1;
        await LoadProducts();
    }

    private async Task GoToPage(int pageNumber)
    {
        _searchRequest.Page = pageNumber;
        await LoadProducts();
    }

    private async Task AddToCart(int productId)
    {
        string userId;
        if (string.IsNullOrEmpty(_userId))
        {
            // Utiliser un ID invité pour les utilisateurs non connectés
            userId = await GetOrCreateGuestUserId();
        }
        else
        {
            userId = _userId;
        }

        var cartItem = new CartItemCreateDto { ProductId = productId, Quantity = 1 };
        await CartService.AddItemToCartAsync(userId, cartItem);
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Guid.NewGuid():N}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }
}
