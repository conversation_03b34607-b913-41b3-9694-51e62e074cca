using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Services;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.DTOs;
using System.Security.Claims;

namespace NafaPlace.Reviews.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin,Moderator")]
public class ModerationController : ControllerBase
{
    private readonly IReviewService _reviewService;

    public ModerationController(IReviewService reviewService)
    {
        _reviewService = reviewService;
    }

    [HttpGet("reviews/status/{status}")]
    public async Task<ActionResult<ReviewsPagedResult>> GetReviewsByStatus(
        string status, 
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10)
    {
        if (!Enum.TryParse<ReviewStatus>(status, out var reviewStatus))
        {
            return BadRequest($"Invalid review status: {status}");
        }
        
        var reviews = await _reviewService.GetReviewsByStatusAsync(reviewStatus, page, pageSize);
        return Ok(reviews);
    }

    [HttpPut("reviews/{id}/status")]
    public async Task<ActionResult<ReviewDto>> UpdateReviewStatus(
        int id, 
        [FromBody] UpdateReviewStatusRequest request)
    {
        try
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
                return Unauthorized();

            if (!Enum.TryParse<ReviewStatus>(request.Status, out var reviewStatus))
            {
                return BadRequest($"Invalid review status: {request.Status}");
            }
            
            var review = await _reviewService.UpdateReviewStatusAsync(id, reviewStatus, currentUserId, request.Notes);
            return Ok(review);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpGet("search")]
    public async Task<ActionResult<ReviewsPagedResult>> SearchReviews(
        [FromQuery] string? searchTerm = null,
        [FromQuery] int? productId = null,
        [FromQuery] int? minRating = null,
        [FromQuery] int? maxRating = null,
        [FromQuery] bool? isVerifiedOnly = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        var results = await _reviewService.SearchReviewsAsync(
            searchTerm ?? string.Empty, 
            productId, 
            minRating, 
            maxRating, 
            isVerifiedOnly, 
            page, 
            pageSize);
            
        return Ok(results);
    }
}

public class UpdateReviewStatusRequest
{
    public string Status { get; set; } = string.Empty;
    public string? Notes { get; set; }
}



