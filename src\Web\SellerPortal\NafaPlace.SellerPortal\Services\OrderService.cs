using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.JSInterop;
using NafaPlace.SellerPortal.Models.Orders;

namespace NafaPlace.SellerPortal.Services;

// DTOs pour l'API Order
public class ApiOrderDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public int Status { get; set; } // Enum OrderStatus
    public int PaymentMethod { get; set; } // Enum PaymentMethod
    public int PaymentStatus { get; set; } // Enum PaymentStatus
    public string? PaymentTransactionId { get; set; }
    public DateTime? PaymentDate { get; set; }
    public ApiShippingAddressDto? ShippingAddress { get; set; }
    public List<ApiOrderItemDto>? OrderItems { get; set; }
}

public class ApiOrderItemDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; }
    public int Quantity { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
}

public class ApiShippingAddressDto
{
    public string FullName { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
}

public class OrderService : IOrderService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IJSRuntime _jsRuntime;
    private readonly IConfiguration _configuration;

    public OrderService(IHttpClientFactory httpClientFactory, IJSRuntime jsRuntime, IConfiguration configuration)
    {
        _httpClientFactory = httpClientFactory;
        _jsRuntime = jsRuntime;
        _configuration = configuration;
    }

    public async Task<OrdersPagedResponse> GetOrdersAsync(OrderFilterRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(request.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
            if (!string.IsNullOrEmpty(request.Status))
                queryParams.Add($"status={Uri.EscapeDataString(request.Status)}");
            if (request.StartDate.HasValue)
                queryParams.Add($"startDate={request.StartDate.Value:yyyy-MM-dd}");
            if (request.EndDate.HasValue)
                queryParams.Add($"endDate={request.EndDate.Value:yyyy-MM-dd}");
            if (request.SellerId.HasValue)
                queryParams.Add($"sellerId={request.SellerId.Value}");
            
            queryParams.Add($"pageNumber={request.PageNumber}");
            queryParams.Add($"pageSize={request.PageSize}");

            var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            
            var response = await httpClient.GetAsync($"{apiUrl}/api/orders{queryString}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                // L'API retourne IEnumerable<Domain.Order>, nous devons le convertir
                var apiOrders = JsonSerializer.Deserialize<List<ApiOrderDto>>(content, options) ?? new List<ApiOrderDto>();

                // Convertir vers notre modèle
                var orders = apiOrders.Select(apiOrder => new Order
                {
                    Id = apiOrder.Id,
                    OrderNumber = $"ORD-{apiOrder.Id:D3}",
                    CustomerName = apiOrder.ShippingAddress?.FullName ?? "Client API",
                    CustomerEmail = apiOrder.UserId,
                    CustomerPhone = apiOrder.ShippingAddress?.PhoneNumber ?? "",
                    OrderDate = apiOrder.OrderDate,
                    ShippingAddress = apiOrder.ShippingAddress?.Address ?? "",
                    ShippingCity = apiOrder.ShippingAddress?.City ?? "",
                    ShippingMethod = "Standard",
                    ShippingFee = 0,
                    PaymentMethod = MapPaymentMethod(apiOrder.PaymentMethod),
                    Status = MapOrderStatus(apiOrder.Status.ToString()),
                    PaymentStatus = MapPaymentStatus(apiOrder.PaymentStatus.ToString()),
                    Subtotal = apiOrder.TotalAmount,
                    TotalAmount = apiOrder.TotalAmount,
                    Notes = "",
                    Items = apiOrder.OrderItems?.Select(item => new OrderItem
                    {
                        Id = item.Id,
                        ProductId = item.ProductId,
                        ProductName = item.ProductName,
                        ProductImageUrl = item.ImageUrl ?? "",
                        UnitPrice = item.UnitPrice,
                        Quantity = item.Quantity
                    }).ToList() ?? new List<OrderItem>(),
                    SellerId = request.SellerId ?? 0,
                    SellerName = "Vendeur API"
                }).ToList();

                return new OrdersPagedResponse
                {
                    Orders = orders,
                    TotalCount = orders.Count,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }

            // Retourner une réponse vide au lieu des données de démonstration
            return new OrdersPagedResponse
            {
                Orders = new List<Order>(),
                TotalCount = 0,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des commandes: {ex.Message}");
            // Retourner une réponse vide au lieu des données de démonstration
            return new OrdersPagedResponse
            {
                Orders = new List<Order>(),
                TotalCount = 0,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
    }

    public async Task<Order?> GetOrderByIdAsync(int orderId)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var response = await httpClient.GetAsync($"{apiUrl}/api/orders/{orderId}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<Order>(content, options);
            }

            // Retourner null si la commande n'est pas trouvée
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de la commande {orderId}: {ex.Message}");
            // Retourner null en cas d'erreur
            return null;
        }
    }

    public async Task<bool> UpdateOrderStatusAsync(UpdateOrderStatusRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var response = await httpClient.PutAsync($"{apiUrl}/api/orders/{request.OrderId}/status", content);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<bool> UpdatePaymentStatusAsync(UpdatePaymentStatusRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var response = await httpClient.PutAsync($"{apiUrl}/api/orders/{request.OrderId}/payment-status", content);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<bool> UpdateOrderNotesAsync(int orderId, string notes)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var request = new { Notes = notes };
            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var response = await httpClient.PutAsync($"{apiUrl}/api/orders/{orderId}/notes", content);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<bool> CancelOrderAsync(int orderId)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var response = await httpClient.PutAsync($"{apiUrl}/api/orders/{orderId}/cancel", null);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<byte[]> ExportOrdersAsync(OrderFilterRequest request, string format = "excel")
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var exportRequest = new
            {
                request.SearchTerm,
                request.Status,
                request.StartDate,
                request.EndDate,
                request.SellerId,
                Format = format
            };

            var json = JsonSerializer.Serialize(exportRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var response = await httpClient.PostAsync($"{apiUrl}/api/orders/export", content);
            
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsByteArrayAsync();
            }
            
            return Array.Empty<byte>();
        }
        catch (Exception)
        {
            return Array.Empty<byte>();
        }
    }

    private async Task SetAuthorizationHeaderAsync(HttpClient httpClient)
    {
        try
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (!string.IsNullOrEmpty(token))
            {
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }
        }
        catch (Exception)
        {
            // Handle JS interop errors silently
        }
    }

    private OrdersPagedResponse GetDemoOrdersData(OrderFilterRequest request)
    {
        var allOrders = GetDemoOrders();
        
        // Apply filters
        var filteredOrders = allOrders.AsQueryable();
        
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            filteredOrders = filteredOrders.Where(o => 
                o.OrderNumber.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.CustomerName.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.CustomerEmail.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase));
        }
        
        if (!string.IsNullOrEmpty(request.Status))
        {
            filteredOrders = filteredOrders.Where(o => o.Status == request.Status);
        }
        
        if (request.StartDate.HasValue)
        {
            filteredOrders = filteredOrders.Where(o => o.OrderDate.Date >= request.StartDate.Value.Date);
        }
        
        if (request.EndDate.HasValue)
        {
            filteredOrders = filteredOrders.Where(o => o.OrderDate.Date <= request.EndDate.Value.Date);
        }

        var totalCount = filteredOrders.Count();
        var orders = filteredOrders
            .OrderByDescending(o => o.OrderDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        return new OrdersPagedResponse
        {
            Orders = orders,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }

    private List<Order> GetDemoOrders()
    {
        return new List<Order>
        {
            new Order
            {
                Id = 1,
                OrderNumber = "ORD-001",
                CustomerName = "Amadou Diallo",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+224 621 12 34 56",
                OrderDate = DateTime.Now.AddDays(-1),
                ShippingAddress = "Quartier Almamya, Rue KA-020",
                ShippingCity = "Conakry",
                ShippingMethod = "Livraison standard",
                ShippingFee = 5000,
                PaymentMethod = "Mobile Money",
                PaymentStatus = "Payé",
                Status = "En attente",
                Subtotal = 45000,
                TotalAmount = 50000,
                Notes = "",
                SellerId = 1,
                SellerName = "Vendeur Test",
                Items = new List<OrderItem>
                {
                    new OrderItem { Id = 1, ProductId = 1, ProductName = "Smartphone XYZ", ProductImageUrl = "/images/products/smartphone.jpg", UnitPrice = 150000, Quantity = 1 }
                }
            },
            new Order
            {
                Id = 2,
                OrderNumber = "ORD-002",
                CustomerName = "Fatou Camara",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+221 77 123 45 67",
                OrderDate = DateTime.Now.AddDays(-2),
                ShippingAddress = "Rue 10 x Avenue Blaise Diagne",
                ShippingCity = "Dakar",
                ShippingMethod = "Livraison express",
                ShippingFee = 8500,
                PaymentMethod = "Carte bancaire",
                PaymentStatus = "Payé",
                Status = "Expédié",
                Subtotal = 70000,
                TotalAmount = 78500,
                Notes = "Client fidèle, livraison prioritaire",
                SellerId = 1,
                SellerName = "Vendeur Test",
                Items = new List<OrderItem>
                {
                    new OrderItem { Id = 2, ProductId = 2, ProductName = "T-shirt Coton", ProductImageUrl = "/images/products/tshirt.jpg", UnitPrice = 8000, Quantity = 2 },
                    new OrderItem { Id = 3, ProductId = 3, ProductName = "Robe d'Été", ProductImageUrl = "/images/products/dress.jpg", UnitPrice = 15000, Quantity = 1 }
                }
            }
        };
    }

    private static string MapOrderStatus(string apiStatus)
    {
        return apiStatus switch
        {
            "0" or "Pending" => "En attente",
            "1" or "Paid" => "Payé",
            "2" or "Shipped" => "Expédié",
            "3" or "Delivered" => "Livré",
            "4" or "Cancelled" => "Annulé",
            _ => "En attente"
        };
    }

    private static string MapPaymentStatus(string apiPaymentStatus)
    {
        return apiPaymentStatus switch
        {
            "0" or "Pending" => "En attente",
            "1" or "Processing" => "En cours",
            "2" or "Completed" => "Payé",
            "3" or "Failed" => "Échoué",
            "4" or "Cancelled" => "Annulé",
            "5" or "Refunded" => "Remboursé",
            _ => "En attente"
        };
    }

    private static string MapPaymentMethod(int apiPaymentMethod)
    {
        return apiPaymentMethod switch
        {
            0 => "Carte bancaire",
            1 => "Orange Money",
            2 => "Virement bancaire",
            3 => "Espèces",
            _ => "Carte bancaire"
        };
    }
}
