using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Moq;
using NafaPlace.Identity.Application.DTOs.Role;
using NafaPlace.Identity.Application.Services;
using NafaPlace.Identity.Domain.Models;
using Xunit;

namespace NafaPlace.Identity.Tests.Roles
{
    public class RoleServiceTests
    {
        [Fact]
        public async Task CreateRole_WithValidData_ReturnsSuccess()
        {
            // Arrange
            var roleManagerMock = new Mock<RoleManager<IdentityRole>>();
            var roleService = new RoleService(roleManagerMock.Object);

            var roleDto = new CreateRoleDto { Name = "Vendeur" };

            roleManagerMock.Setup(x => x.CreateAsync(It.IsAny<IdentityRole>()))
                .ReturnsAsync(IdentityResult.Success);

            // Act
            var result = await roleService.CreateRoleAsync(roleDto);

            // Assert
            Assert.True(result.Succeeded);
        }

        [Fact]
        public async Task AssignRoleToUser_WithValidData_ReturnsSuccess()
        {
            // Arrange
            var userManagerMock = new Mock<UserManager<ApplicationUser>>();
            var roleManagerMock = new Mock<RoleManager<IdentityRole>>();
            var roleService = new RoleService(roleManagerMock.Object, userManagerMock.Object);

            var user = new ApplicationUser { Id = "user1", Email = "<EMAIL>" };
            var roleName = "Vendeur";

            userManagerMock.Setup(x => x.FindByIdAsync(user.Id))
                .ReturnsAsync(user);

            userManagerMock.Setup(x => x.AddToRoleAsync(user, roleName))
                .ReturnsAsync(IdentityResult.Success);

            // Act
            var result = await roleService.AssignRoleToUserAsync(user.Id, roleName);

            // Assert
            Assert.True(result.Succeeded);
        }

        [Fact]
        public async Task GetUserRoles_ReturnsListOfRoles()
        {
            // Arrange
            var userManagerMock = new Mock<UserManager<ApplicationUser>>();
            var roleManagerMock = new Mock<RoleManager<IdentityRole>>();
            var roleService = new RoleService(roleManagerMock.Object, userManagerMock.Object);

            var user = new ApplicationUser { Id = "user1", Email = "<EMAIL>" };
            var roles = new List<string> { "Client", "Vendeur" };

            userManagerMock.Setup(x => x.FindByIdAsync(user.Id))
                .ReturnsAsync(user);

            userManagerMock.Setup(x => x.GetRolesAsync(user))
                .ReturnsAsync(roles);

            // Act
            var result = await roleService.GetUserRolesAsync(user.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(roles.Count, result.Count);
            Assert.Contains("Client", result);
            Assert.Contains("Vendeur", result);
        }

        [Fact]
        public async Task RemoveRoleFromUser_WithValidData_ReturnsSuccess()
        {
            // Arrange
            var userManagerMock = new Mock<UserManager<ApplicationUser>>();
            var roleManagerMock = new Mock<RoleManager<IdentityRole>>();
            var roleService = new RoleService(roleManagerMock.Object, userManagerMock.Object);

            var user = new ApplicationUser { Id = "user1", Email = "<EMAIL>" };
            var roleName = "Vendeur";

            userManagerMock.Setup(x => x.FindByIdAsync(user.Id))
                .ReturnsAsync(user);

            userManagerMock.Setup(x => x.RemoveFromRoleAsync(user, roleName))
                .ReturnsAsync(IdentityResult.Success);

            // Act
            var result = await roleService.RemoveRoleFromUserAsync(user.Id, roleName);

            // Assert
            Assert.True(result.Succeeded);
        }
    }
}
