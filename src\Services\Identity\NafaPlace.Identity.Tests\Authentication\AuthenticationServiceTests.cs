using NafaPlace.Identity.Application.Services;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Application.DTOs.Auth;
using Xunit;
using Moq;
using Microsoft.AspNetCore.Identity;
using System.Threading.Tasks;

namespace NafaPlace.Identity.Tests.Authentication
{
    public class AuthenticationServiceTests
    {
        [Fact]
        public async Task Login_WithValidCredentials_ReturnsAuthResponse()
        {
            // Arrange
            var userManagerMock = new Mock<UserManager<ApplicationUser>>();
            var signInManagerMock = new Mock<SignInManager<ApplicationUser>>();
            var jwtServiceMock = new Mock<IJwtService>();
            
            var authService = new AuthenticationService(
                userManagerMock.Object,
                signInManagerMock.Object,
                jwtServiceMock.Object);

            var loginDto = new LoginDto 
            { 
                Email = "<EMAIL>",
                Password = "Password123!"
            };

            var user = new ApplicationUser 
            { 
                Email = loginDto.Email,
                UserName = loginDto.Email
            };

            userManagerMock.Setup(x => x.FindByEmailAsync(loginDto.Email))
                .ReturnsAsync(user);

            signInManagerMock.Setup(x => x.CheckPasswordSignInAsync(user, loginDto.Password, false))
                .ReturnsAsync(SignInResult.Success);

            jwtServiceMock.Setup(x => x.GenerateToken(user))
                .Returns("test-jwt-token");

            // Act
            var result = await authService.LoginAsync(loginDto);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Token);
            Assert.Equal(user.Email, result.Email);
        }

        [Fact]
        public async Task Login_WithInvalidCredentials_ReturnsNull()
        {
            // Arrange
            var userManagerMock = new Mock<UserManager<ApplicationUser>>();
            var signInManagerMock = new Mock<SignInManager<ApplicationUser>>();
            var jwtServiceMock = new Mock<IJwtService>();
            
            var authService = new AuthenticationService(
                userManagerMock.Object,
                signInManagerMock.Object,
                jwtServiceMock.Object);

            var loginDto = new LoginDto 
            { 
                Email = "<EMAIL>",
                Password = "WrongPassword123!"
            };

            userManagerMock.Setup(x => x.FindByEmailAsync(loginDto.Email))
                .ReturnsAsync((ApplicationUser)null);

            // Act
            var result = await authService.LoginAsync(loginDto);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task Register_WithValidData_ReturnsAuthResponse()
        {
            // Arrange
            var userManagerMock = new Mock<UserManager<ApplicationUser>>();
            var signInManagerMock = new Mock<SignInManager<ApplicationUser>>();
            var jwtServiceMock = new Mock<IJwtService>();
            
            var authService = new AuthenticationService(
                userManagerMock.Object,
                signInManagerMock.Object,
                jwtServiceMock.Object);

            var registerDto = new RegisterDto 
            { 
                Email = "<EMAIL>",
                Password = "Password123!",
                FirstName = "Test",
                LastName = "User"
            };

            var user = new ApplicationUser 
            { 
                Email = registerDto.Email,
                UserName = registerDto.Email,
                FirstName = registerDto.FirstName,
                LastName = registerDto.LastName
            };

            userManagerMock.Setup(x => x.CreateAsync(It.IsAny<ApplicationUser>(), registerDto.Password))
                .ReturnsAsync(IdentityResult.Success);

            jwtServiceMock.Setup(x => x.GenerateToken(It.IsAny<ApplicationUser>()))
                .Returns("test-jwt-token");

            // Act
            var result = await authService.RegisterAsync(registerDto);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Token);
            Assert.Equal(registerDto.Email, result.Email);
        }
    }
}
