<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NafaPlace.Inventory.Domain\NafaPlace.Inventory.Domain.csproj" />
    <ProjectReference Include="..\NafaPlace.Inventory.Application\NafaPlace.Inventory.Application.csproj" />
  </ItemGroup>

</Project>
