{"ApiSettings": {"BaseUrl": "http://localhost:5000", "CatalogApiUrl": "http://localhost:5243", "IdentityApiUrl": "http://localhost:5155", "OrderApiUrl": "http://localhost:5004", "CartApiUrl": "http://localhost:5003", "PaymentApiUrl": "http://localhost:5005", "ReviewApiUrl": "http://localhost:5006", "InventoryApiUrl": "http://localhost:5009"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}}