using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using NafaPlace.Identity.Application.DTOs.Auth;
using NafaPlace.Identity.Infrastructure.Persistence;
using Xunit;

namespace NafaPlace.Identity.IntegrationTests.Controllers;

public class AuthControllerTests : IClassFixture<CustomWebApplicationFactory>
{
    private readonly CustomWebApplicationFactory _factory;
    private readonly HttpClient _client;

    public AuthControllerTests(CustomWebApplicationFactory factory)
    {
        _factory = factory;
        _client = factory.CreateClient();
    }

    [Fact]
    public async Task Register_WithValidRequest_ShouldReturnSuccess()
    {
        // Arrange
        var request = new RegisterRequest
        {
            Email = "<EMAIL>",
            Username = "testuser",
            Password = "Password123!",
            PhoneNumber = "1234567890"
        };

        var content = new StringContent(
            JsonSerializer.Serialize(request),
            Encoding.UTF8,
            "application/json");

        // Act
        var response = await _client.PostAsync("/api/auth/register", content);
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<AuthResponse>(
            responseString,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.NotNull(result);
        Assert.NotNull(result.AccessToken);
        Assert.NotNull(result.RefreshToken);
        Assert.Equal(request.Email, result.User.Email);
        Assert.Equal(request.Username, result.User.Username);
    }

    [Fact]
    public async Task Register_WithExistingEmail_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new RegisterRequest
        {
            Email = "<EMAIL>",
            Username = "existinguser",
            Password = "Password123!",
            PhoneNumber = "1234567890"
        };

        var content = new StringContent(
            JsonSerializer.Serialize(request),
            Encoding.UTF8,
            "application/json");

        // Premier enregistrement
        await _client.PostAsync("/api/auth/register", content);

        // Deuxième enregistrement avec le même email
        var response = await _client.PostAsync("/api/auth/register", content);

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    public async Task Login_WithValidCredentials_ShouldReturnSuccess()
    {
        // Arrange
        var registerRequest = new RegisterRequest
        {
            Email = "<EMAIL>",
            Username = "loginuser",
            Password = "Password123!",
            PhoneNumber = "1234567890"
        };

        var registerContent = new StringContent(
            JsonSerializer.Serialize(registerRequest),
            Encoding.UTF8,
            "application/json");

        await _client.PostAsync("/api/auth/register", registerContent);

        var loginRequest = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "Password123!"
        };

        var loginContent = new StringContent(
            JsonSerializer.Serialize(loginRequest),
            Encoding.UTF8,
            "application/json");

        // Act
        var response = await _client.PostAsync("/api/auth/login", loginContent);
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<AuthResponse>(
            responseString,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.NotNull(result);
        Assert.NotNull(result.AccessToken);
        Assert.NotNull(result.RefreshToken);
        Assert.Equal(loginRequest.Email, result.User.Email);
    }

    [Fact]
    public async Task RefreshToken_WithValidToken_ShouldReturnNewTokens()
    {
        // Arrange
        var registerRequest = new RegisterRequest
        {
            Email = "<EMAIL>",
            Username = "refreshuser",
            Password = "Password123!",
            PhoneNumber = "1234567890"
        };

        var registerContent = new StringContent(
            JsonSerializer.Serialize(registerRequest),
            Encoding.UTF8,
            "application/json");

        var registerResponse = await _client.PostAsync("/api/auth/register", registerContent);
        var registerResult = JsonSerializer.Deserialize<AuthResponse>(
            await registerResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        var refreshRequest = new RefreshTokenRequest
        {
            RefreshToken = registerResult.RefreshToken
        };

        var refreshContent = new StringContent(
            JsonSerializer.Serialize(refreshRequest),
            Encoding.UTF8,
            "application/json");

        // Act
        var response = await _client.PostAsync("/api/auth/refresh-token", refreshContent);
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<AuthResponse>(
            responseString,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.NotNull(result);
        Assert.NotNull(result.AccessToken);
        Assert.NotNull(result.RefreshToken);
        Assert.NotEqual(registerResult.AccessToken, result.AccessToken);
        Assert.NotEqual(registerResult.RefreshToken, result.RefreshToken);
    }

    [Fact]
    public async Task Logout_WithValidToken_ShouldReturnSuccess()
    {
        // Arrange
        var registerRequest = new RegisterRequest
        {
            Email = "<EMAIL>",
            Username = "logoutuser",
            Password = "Password123!",
            PhoneNumber = "1234567890"
        };

        var registerContent = new StringContent(
            JsonSerializer.Serialize(registerRequest),
            Encoding.UTF8,
            "application/json");

        var registerResponse = await _client.PostAsync("/api/auth/register", registerContent);
        var registerResult = JsonSerializer.Deserialize<AuthResponse>(
            await registerResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        _client.DefaultRequestHeaders.Authorization = 
            new AuthenticationHeaderValue("Bearer", registerResult.AccessToken);

        var logoutRequest = new RefreshTokenRequest
        {
            RefreshToken = registerResult.RefreshToken
        };

        var logoutContent = new StringContent(
            JsonSerializer.Serialize(logoutRequest),
            Encoding.UTF8,
            "application/json");

        // Act
        var response = await _client.PostAsync("/api/auth/logout", logoutContent);

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);

        // Verify refresh token is revoked
        var refreshContent = new StringContent(
            JsonSerializer.Serialize(new RefreshTokenRequest { RefreshToken = registerResult.RefreshToken }),
            Encoding.UTF8,
            "application/json");

        var refreshResponse = await _client.PostAsync("/api/auth/refresh-token", refreshContent);
        Assert.Equal(HttpStatusCode.BadRequest, refreshResponse.StatusCode);
    }

    [Fact]
    public async Task CompleteUserFlow_ShouldWorkAsExpected()
    {
        // 1. Register
        var registerRequest = new RegisterRequest
        {
            Email = "<EMAIL>",
            Username = "flowuser",
            Password = "Password123!",
            PhoneNumber = "1234567890"
        };

        var registerContent = new StringContent(
            JsonSerializer.Serialize(registerRequest),
            Encoding.UTF8,
            "application/json");

        var registerResponse = await _client.PostAsync("/api/auth/register", registerContent);
        Assert.Equal(HttpStatusCode.OK, registerResponse.StatusCode);
        var registerResult = JsonSerializer.Deserialize<AuthResponse>(
            await registerResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // 2. Login
        var loginRequest = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "Password123!"
        };

        var loginContent = new StringContent(
            JsonSerializer.Serialize(loginRequest),
            Encoding.UTF8,
            "application/json");

        var loginResponse = await _client.PostAsync("/api/auth/login", loginContent);
        Assert.Equal(HttpStatusCode.OK, loginResponse.StatusCode);
        var loginResult = JsonSerializer.Deserialize<AuthResponse>(
            await loginResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // 3. Refresh Token
        var refreshRequest = new RefreshTokenRequest
        {
            RefreshToken = loginResult.RefreshToken
        };

        var refreshContent = new StringContent(
            JsonSerializer.Serialize(refreshRequest),
            Encoding.UTF8,
            "application/json");

        var refreshResponse = await _client.PostAsync("/api/auth/refresh-token", refreshContent);
        Assert.Equal(HttpStatusCode.OK, refreshResponse.StatusCode);
        var refreshResult = JsonSerializer.Deserialize<AuthResponse>(
            await refreshResponse.Content.ReadAsStringAsync(),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        // 4. Logout
        _client.DefaultRequestHeaders.Authorization = 
            new AuthenticationHeaderValue("Bearer", refreshResult.AccessToken);

        var logoutRequest = new RefreshTokenRequest
        {
            RefreshToken = refreshResult.RefreshToken
        };

        var logoutContent = new StringContent(
            JsonSerializer.Serialize(logoutRequest),
            Encoding.UTF8,
            "application/json");

        var logoutResponse = await _client.PostAsync("/api/auth/logout", logoutContent);
        Assert.Equal(HttpStatusCode.OK, logoutResponse.StatusCode);

        // 5. Verify Cannot Use Old Token
        var finalRefreshContent = new StringContent(
            JsonSerializer.Serialize(new RefreshTokenRequest { RefreshToken = refreshResult.RefreshToken }),
            Encoding.UTF8,
            "application/json");

        var finalRefreshResponse = await _client.PostAsync("/api/auth/refresh-token", finalRefreshContent);
        Assert.Equal(HttpStatusCode.BadRequest, finalRefreshResponse.StatusCode);
    }
}
